<?php

/**
 * Portfolio Score Calculator (static simulation)
 *
 * - Provides 6 sub-metric scores and a weighted total score for a portfolio
 * - Uses existing metric keys in API response to avoid breaking clients
 * - For now, produces static/deterministic values to simulate behavior
 */

// Unvetted coin'ler için sabit <PERSON> s<PERSON> (nötr <PERSON>)
define('UNVETTED_COIN_AI_SCORE', 50.0);

/**
 * Returns weight coefficients for each portfolio metric.
 * Total must equal 1.0
 *
 * Mapping to business doc:
 * - portfolio_quality        => portfoy_kalite_skoru (W_Kalite = 0.25)
 * - marketCapDistribution => piyasa_degeri_skoru (W_MarketCap = 0.20)
 * - trend_category         => trend_kategori_skoru (W_Trend = 0.15)
 * - category_diversification       => kategori_cesitlilik_skoru (W_Cesitlilik = 0.15)
 * - portfolio_weight     => portfoy_agirlik_skoru (W_Agirlik = 0.15)
 * - stablecoinRatio       => stabilcoin_oran_skoru (W_Stabilcoin = 0.10)
 */
function portfolio_score_weights()
{
    return [
        'portfolio_quality' => 0.30,        // W_Kalite
        'marketCapDistribution' => 0.15, // W_MarketCap
        'trend_category' => 0.15,         // W_Trend
        'category_diversification' => 0.15,       // W_Cesitlilik
        'portfolio_weight' => 0.15,     // W_Agirlik
        'stablecoinRatio' => 0.10        // W_Stabilcoin
    ];
}

/**
 * Simulate per-metric scores (0-100) for the portfolio.
 * You can later replace internals with real calculations using $context.
 *
 * @param array|null $context Optional portfolio context: assets, totals, etc.
 * @return array<string,float> Metric key => score
 */
function simulate_metric_scores($context = null)
{
    // Optionally derive some basic determinants to vary scores a bit
    $assetCount = isset($context['assetCount']) ? (int)$context['assetCount'] : (isset($context['assets']) ? count((array)$context['assets']) : 0);

    // Base static scores (1 = statik, gerçek hesaplama yapılmamış)
    $scores = [
        'portfolio_quality' => 1,
        'marketCapDistribution' => 1,
        'trend_category' => 1, // will be refined using gecko_categories if categories provided
        'category_diversification' => 1,
        'portfolio_weight' => 1,
        'stablecoinRatio' => 1
    ];

    // Simple deterministic tweak based on asset count (keeps values in 0-100)
    if ($assetCount > 0) {
        $delta = max(-5, min(5, ($assetCount - 6))); // small tilt around 6 assets
        $scores['category_diversification'] = max(0, min(100, $scores['category_diversification'] + $delta));
        $scores['portfolio_weight'] = max(0, min(100, $scores['portfolio_weight'] + ($delta / 2)));
    }

    return $scores;
}

/**
 * Collect distinct category names from portfolio assets (from coindata_categories.use_for_portfolio=1)
 *
 * @param array|null $context
 * @return array<int,string> Distinct category names
 */
function get_portfolio_distinct_categories($context = null)
{
    $names = [];
    if (!is_array($context) || !isset($context['assets']) || !is_array($context['assets'])) {
        return $names;
    }
    foreach ($context['assets'] as $asset) {
        if (isset($asset['categories']) && is_array($asset['categories'])) {
            foreach ($asset['categories'] as $cat) {
                $name = is_array($cat) ? trim((string)($cat['name'] ?? '')) : trim((string)$cat);
                if ($name !== '') {
                    $names[$name] = true;
                }
            }
        }
    }
    return array_keys($names);
}



/**
 * Fetch quality scores (avg_total_ai_score) for given category names from gecko_categories
 *
 * @param array<int,string> $categoryNames
 * @return array<string,float> name => quality (0-100)
 */
function fetch_category_quality_scores($categoryNames)
{
    global $link;
    $result = [];
    if (!is_array($categoryNames) || empty($categoryNames)) return $result;
    $escaped = [];
    foreach ($categoryNames as $n) {
        $n = trim((string)$n);
        if ($n === '') continue;
        $escaped[] = "'" . mysqli_real_escape_string($link, $n) . "'";
    }
    if (empty($escaped)) return $result;
    $inList = implode(',', $escaped);
    $sql = "SELECT name, avg_total_ai_score FROM gecko_categories WHERE name IN ($inList)";
    $rs = mysqli_query($link, $sql);
    if ($rs) {
        while ($row = mysqli_fetch_assoc($rs)) {
            $name = (string)$row['name'];
            $val = isset($row['avg_total_ai_score']) ? (float)$row['avg_total_ai_score'] : null;
            if ($val !== null) {
                $result[$name] = max(0.0, min(100.0, $val));
            }
        }
    }
    return $result;
}

/**
 * Get 30D market cap change for a category using gecko_categories_details table
 * Finds the closest date to 30 days ago and calculates percentage change
 *
 * @param string $categoryName
 * @param string|null $gid gecko_id for the category
 * @return float percentage change (e.g. 12.3 means +12.3%), returns 0 if data unavailable
 */
function generate_category_change_by_days($categoryName, $gid = null, $days = 30)
{
    global $link;
    require_once __DIR__ . '/../stripe/StripeLogger.php';

    if (empty($gid)) {
        StripeLogger::log('Category change by days: No gid provided', 'HIGH', [
            'category' => $categoryName,
            'reason' => 'missing_gid'
        ]);
        return 0.0;
    }

    // Get current market cap
    $currentSql = "SELECT marketcap FROM gecko_categories_details WHERE gecko_id = ? ORDER BY market_date DESC LIMIT 1";
    $currentStmt = mysqli_prepare($link, $currentSql);
    $currentMarketCap = null;

    if ($currentStmt) {
        mysqli_stmt_bind_param($currentStmt, 's', $gid);
        if (mysqli_stmt_execute($currentStmt)) {
            $result = mysqli_stmt_get_result($currentStmt);
            if ($row = mysqli_fetch_assoc($result)) {
                $currentMarketCap = (float)$row['marketcap'];
            }
        }
        mysqli_stmt_close($currentStmt);
    }

    if ($currentMarketCap === null || $currentMarketCap <= 0) {
        StripeLogger::log('Category change by days: No current market cap data', 'HIGH', [
            'category' => $categoryName,
            'gid' => $gid,
            'reason' => 'no_current_data'
        ]);
        return 0.0;
    }

    // Get market cap from X days ago
    $targetDate = date('Y-m-d', strtotime("-{$days} days"));
    $oldSql = "SELECT marketcap FROM gecko_categories_details
               WHERE gecko_id = ? AND market_date <= ?
               ORDER BY market_date DESC LIMIT 1";
    $oldStmt = mysqli_prepare($link, $oldSql);
    $oldMarketCap = null;

    if ($oldStmt) {
        mysqli_stmt_bind_param($oldStmt, 'ss', $gid, $targetDate);
        if (mysqli_stmt_execute($oldStmt)) {
            $result = mysqli_stmt_get_result($oldStmt);
            if ($row = mysqli_fetch_assoc($result)) {
                $oldMarketCap = (float)$row['marketcap'];
            }
        }
        mysqli_stmt_close($oldStmt);
    }

    if ($oldMarketCap === null || $oldMarketCap <= 0) {
        StripeLogger::log('Category change by days: No historical market cap data', 'HIGH', [
            'category' => $categoryName,
            'gid' => $gid,
            'target_date' => $targetDate,
            'days' => $days,
            'reason' => 'no_historical_data'
        ]);
        return 0.0;
    }

    // Calculate percentage change
    $change = (($currentMarketCap - $oldMarketCap) / $oldMarketCap) * 100.0;
    return round($change, 2);
}


/**
 * Compute momentum scores (0-100) from raw 30D changes by min-max normalization across categories
 * Highest change => 100, lowest => 0
 * If all equal, assign 50 to all
 *
 * @param array<int,string|array> $categoryNames Array of category names or category objects with gid
 * @return array<string,array{change:float, momentum:float}> name => {change, momentum}
 */
function compute_category_momentum_scores($categoryNames)
{
    $raw = [];
    foreach ($categoryNames as $cat) {
        if (is_array($cat)) {
            $n = (string)($cat['name'] ?? '');
            $gid = $cat['gid'] ?? null;
        } else {
            $n = (string)$cat;
            $gid = null;
        }
        if ($n === '') continue;
        $raw[$n] = generate_category_change_by_days($n, $gid, 7);
    }
    if (empty($raw)) return [];
    $min = min($raw);
    $max = max($raw);
    $out = [];
    if ($max == $min) {
        foreach ($raw as $n => $v) { $out[$n] = ['change' => $v, 'momentum' => 50.0]; }
        return $out;
    }
    $range = $max - $min;
    foreach ($raw as $n => $v) {
        $score = ($v - $min) / $range * 100.0;
        $out[$n] = ['change' => $v, 'momentum' => round(max(0.0, min(100.0, $score)), 2)];
    }
    return $out;
}

/**
 * Compute per-category trend components: quality, momentum, trend
 * trend = 0.60 * quality + 0.40 * momentum
 *
 * @param array|null $context Portfolio context including assets with 'categories'
 * @param float $qualityFallback Fallback quality if not found in DB
 * @return array{
 *   categories: array<string,array{quality:float,momentum:float,trend:float,change30d:float}>,
 *   distinctCount: int
 * }
 */
function compute_categories_trend_components($context = null, $qualityFallback = 60.0)
{
    // Get category objects with gid instead of just names
    $categories = [];
    if (is_array($context) && isset($context['assets']) && is_array($context['assets'])) {
        foreach ($context['assets'] as $asset) {
            if (isset($asset['categories']) && is_array($asset['categories'])) {
                foreach ($asset['categories'] as $cat) {
                    if (is_array($cat) && !empty($cat['name'])) {
                        $name = trim((string)$cat['name']);
                        if ($name !== '') {
                            $categories[$name] = $cat; // Keep full category object with gid
                        }
                    }
                }
            }
        }
    }

    $names = array_keys($categories);
    $qualities = fetch_category_quality_scores($names);
    $momentums = compute_category_momentum_scores(array_values($categories));

    $result = [];
    foreach ($names as $name) {
        $q = isset($qualities[$name]) ? (float)$qualities[$name] : (float)$qualityFallback;
        $m = isset($momentums[$name]['momentum']) ? (float)$momentums[$name]['momentum'] : 50.0;
        $chg = isset($momentums[$name]['change']) ? (float)$momentums[$name]['change'] : 0.0;
        $trend = (0.60 * $q) + (0.40 * $m);
        $result[$name] = round(max(0.0, min(100.0, $trend)), 2);
    }

    return [
        'categories' => $result,
        'distinctCount' => count($names)
    ];
}


/**
 * Calculate weighted total score (0-100)
 *
 * @param array<string,float> $scores Per-metric scores
 * @param array<string,float> $weights Per-metric weights
 * @return float Weighted total
 */
function calculate_weighted_total_score($scores, $weights)
{
    $total = 0.0;
    foreach ($weights as $key => $w) {
        $s = isset($scores[$key]) ? (float)$scores[$key] : 0.0;
        $total += $s * (float)$w;
    }
    return $total; // already in 0-100 scale because scores are 0-100 and weights sum to 1
}

/**
 * Build API-ready metrics structure for getPortfolio()
 *
 * @param array|null $context Optional portfolio context (assets, totals, assetCount)
 * @return array API response metrics block
 */
function build_portfolio_metrics($context = null)
{
    $weights = portfolio_score_weights();
    $scores = simulate_metric_scores($context);

    // Sector balance artık ayrı bir kategori skoru metodundan geliyor
    $catScore = compute_category_scores($context);
    if ($catScore !== null && isset($catScore['score'])) {
        $scores['trend_category'] = (float)$catScore['score'];
    } else {
        // Kategori yoksa veya hesaplanamadıysa mevcut statik değeri kullan
        $scores['trend_category'] = isset($scores['trend_category']) ? (float)$scores['trend_category'] : 1;
    }

    // Kategori çeşitlilik skoru hesapla
    $diversityScore = compute_category_diversity_score($context);
    if ($diversityScore !== null) {
        $scores['category_diversification'] = $diversityScore;
    }

    // Piyasa değeri dağılım skoru hesapla
    $marketCapScore = compute_market_cap_distribution_score($context);
    if ($marketCapScore !== null) {
        $scores['marketCapDistribution'] = $marketCapScore;
    }

    // Portföy ağırlık skoru hesapla
    $weightScore = compute_portfolio_weight_score($context);
    if ($weightScore !== null) {
        $scores['portfolio_weight'] = (float)$weightScore;
    }

    // Portföy kalite skoru hesapla
    $qualityScore = compute_portfolio_quality_score($context);
    if ($qualityScore !== null) {
        $scores['portfolio_quality'] = $qualityScore;
    }

    // Stabilcoin oran skoru hesapla
    $stablecoinScore = compute_stablecoin_ratio_score($context);
    if ($stablecoinScore !== null) {
        $scores['stablecoinRatio'] = (float)$stablecoinScore;
    }

    $totalScore = calculate_weighted_total_score($scores, $weights);

    // Helper to construct a metric entry
    $entry = function ($key, $analysis) use ($scores, $weights) {
        $score = isset($scores[$key]) ? (float)$scores[$key] : 0.0;
        $weight = isset($weights[$key]) ? (float)$weights[$key] : 0.0;
        $contribution = round($score * $weight, 2);
        return [
            'score' => $score,
            'contribution' => $contribution,
            'maxScore' => 100,
            'analysis' => $analysis
        ];
    };

    return [
        'category_diversification' => $entry('category_diversification', 'Kategori çeşitlilik skoru (HHI tabanlı)'),
        'trend_category' => $entry('trend_category', 'Kategori trend skorları baz alınmıştır'),
        'marketCapDistribution' => $entry('marketCapDistribution', 'Piyasa değeri dağılım skoru (Large/Mid/Small-Cap)'),
        'portfolio_weight' => $entry('portfolio_weight', 'Portföy ağırlık skoru (en büyük 5 varlık yoğunlaşması)'),
        'stablecoinRatio' => $entry('stablecoinRatio', 'Stabilcoin oran skoru (optimal %20-25 aralığı)'),
        'portfolio_quality' => $entry('portfolio_quality', 'Portföy kalite skoru (CoinScout proje skorları ağırlıklı ortalaması)'),
        'totalScore' => round($totalScore, 2)
    ];
}

/**
 * Fetch category market caps from gecko_categories
 *
 * @param array<int,string> $categoryNames
 * @return array<string,float> name => market_cap
 */
function fetch_category_market_caps($categoryNames)
{
    global $link;
    $result = [];
    if (!is_array($categoryNames) || empty($categoryNames)) return $result;
    $escaped = [];
    foreach ($categoryNames as $n) {
        $n = trim((string)$n);
        if ($n === '') continue;
        $escaped[] = "'" . mysqli_real_escape_string($link, $n) . "'";
    }
    if (empty($escaped)) return $result;
    $inList = implode(',', $escaped);
    $sql = "SELECT name, market_cap FROM gecko_categories WHERE name IN ($inList)";
    $rs = mysqli_query($link, $sql);
    if ($rs) {
        while ($row = mysqli_fetch_assoc($rs)) {
            $name = (string)$row['name'];
            $val = isset($row['market_cap']) ? (float)$row['market_cap'] : 0.0;
            $result[$name] = max(0.0, $val);
        }
    }
    return $result;
}

/**
 * Asset'in stabilcoin olup olmadığını belirler.
 * Kategorilerinde "stablecoin" geçen varlıklar stabilcoin kabul edilir (case-insensitive).
 *
 * @param array $asset
 * @return bool
 */
function is_asset_stablecoin($asset)
{
    if (!is_array($asset)) return false;
    if (isset($asset['categories']) && is_array($asset['categories'])) {
        foreach ($asset['categories'] as $category) {
            $categoryName = is_array($category) ? trim((string)($category['name'] ?? '')) : trim((string)$category);
            $categoryLower = strtolower($categoryName);
            if ($categoryLower !== '' && strpos($categoryLower, 'stablecoin') !== false) {
                return true;
            }
        }
    }
    return false;
}


/**
 * Stabilcoin oran skoru hesaplama
 * Portföydeki stabilcoin oranının optimal aralıklara ne kadar yakın olduğunu ölçer
 *
 * @param array|null $context
 * @return int|null 0-100 arası stabilcoin oran skoru
 */
function compute_stablecoin_ratio_score($context = null)
{
    if (!is_array($context) || !isset($context['assets']) || !is_array($context['assets'])) {
        return null;
    }

    $totalValue = isset($context['totalValue']) ? (float)$context['totalValue'] : 0.0;
    if ($totalValue <= 0) return null;

    $stablecoinValue = 0.0;

    foreach ($context['assets'] as $asset) {
        $assetValue = isset($asset['currentValue']) ? (float)$asset['currentValue'] : (float)($asset['totalInvested'] ?? 0);
        if ($assetValue <= 0) continue;

        // Stabilcoin kontrolü (ortak helper)
        if (is_asset_stablecoin($asset)) {
            $stablecoinValue += $assetValue;
        }
    }

    // Stabilcoin oranını hesapla
    $stablecoinRatio = $stablecoinValue / $totalValue;
    // Eşiklere göre skor ata
    if ($stablecoinRatio >= 0.15 && $stablecoinRatio <= 0.20) {
        return 95; // Excellent
    } elseif (($stablecoinRatio >= 0.10 && $stablecoinRatio < 0.15) ||
              ($stablecoinRatio > 0.20 && $stablecoinRatio <= 0.25)) {
        return 80; // Good
    } elseif (($stablecoinRatio >= 0.05 && $stablecoinRatio < 0.10) ||
              ($stablecoinRatio > 0.25 && $stablecoinRatio <= 0.35)) {
        return 70; // Average
    } else {
        return 40; // Poor (< 0.05 veya > 0.35)
    }
}

/**
 * Portföy kalite skoru hesaplama
 * Her asset'in CoinScout proje skorunu portföy ağırlığıyla çarparak ağırlıklı ortalama hesaplar
 *
 * @param array|null $context
 * @return float|null 0-100 arası portföy kalite skoru
 */
function compute_portfolio_quality_score($context = null)
{
    if (!is_array($context) || !isset($context['assets']) || !is_array($context['assets'])) {
        return null;
    }

    $totalValue = isset($context['totalValue']) ? (float)$context['totalValue'] : 0.0;
    if ($totalValue <= 0) return null;

    $weightedScore = 0.0;

    foreach ($context['assets'] as $asset) {
        $assetValue = isset($asset['currentValue']) ? (float)$asset['currentValue'] : (float)($asset['totalInvested'] ?? 0);
        if ($assetValue <= 0) continue;

        // Asset'in portföy ağırlığı
        $assetWeight = $assetValue / $totalValue;

        // CoinScout proje skoru (aiScore alanından)
        $isVetted = isset($asset['isVetted']) ? (bool)$asset['isVetted'] : false;
        $projectScore = $isVetted ?
            (isset($asset['aiScore']) ? (float)$asset['aiScore'] : 0.0) :
            UNVETTED_COIN_AI_SCORE;

        // Ağırlıklı skora ekle
        $weightedScore += $assetWeight * $projectScore;
    }

    return round(max(0.0, min(100.0, $weightedScore)), 2);
}

/**
 * Portföy ağırlık skoru hesaplama
 * En büyük 5 varlığın toplam ağırlığına göre yoğunlaşma riskini ölçer
 *
 * @param array|null $context
 * @return int|null 0-100 arası portföy ağırlık skoru
 */
function compute_portfolio_weight_score($context = null)
{
    if (!is_array($context) || !isset($context['assets']) || !is_array($context['assets'])) {
        return null;
    }

    // Stabilcoinleri hariç tutarak toplam değeri hesapla
    $nonStableTotalValue = 0.0;
    foreach ($context['assets'] as $asset) {
        if (is_asset_stablecoin($asset)) continue;
        $assetValue = isset($asset['currentValue']) ? (float)$asset['currentValue'] : (float)($asset['totalInvested'] ?? 0);
        if ($assetValue <= 0) continue;
        $nonStableTotalValue += $assetValue;
    }
    if ($nonStableTotalValue <= 0) return null;

    // Asset ağırlıklarını hesapla (stabilcoinler dahil değil)
    $assetWeights = [];
    foreach ($context['assets'] as $asset) {
        if (is_asset_stablecoin($asset)) continue;
        $assetValue = isset($asset['currentValue']) ? (float)$asset['currentValue'] : (float)($asset['totalInvested'] ?? 0);
        if ($assetValue <= 0) continue;
        $weight = $assetValue / $nonStableTotalValue;
        $assetWeights[] = $weight;
    }

    if (empty($assetWeights)) return null;

    // Büyükten küçüğe sırala
    rsort($assetWeights);

    // En büyük 5 varlığın toplam ağırlığını hesapla
    $top5Weight = 0.0;
    for ($i = 0; $i < min(5, count($assetWeights)); $i++) {
        $top5Weight += $assetWeights[$i];
    }

    // Eşiklere göre skor ata
    if ($top5Weight < 0.50) {
        return 95; // Excellent
    } elseif ($top5Weight < 0.65) {
        return 80; // Good
    } elseif ($top5Weight < 0.80) {
        return 70; // Average
    } else {
        return 40; // Poor
    }
}

/**
 * Piyasa değeri dağılım yüzdeleri hesaplama (Frontend chart için)
 * Large-Cap, Mid-Cap, Small-Cap yüzdelik dağılımını döndürür
 *
 * @param array|null $context
 * @return array|null Market cap kategorisi => yüzde değeri array'i
 */
function compute_market_cap_distribution_breakdown($context = null)
{
    if (!is_array($context) || !isset($context['assets']) || !is_array($context['assets'])) {
        return null;
    }

    // Stabilcoinleri hariç tutarak toplam değeri hesapla
    $nonStableTotalValue = 0.0;
    foreach ($context['assets'] as $asset) {
        if (is_asset_stablecoin($asset)) continue;
        $assetValue = isset($asset['currentValue']) ? (float)$asset['currentValue'] : (float)($asset['totalInvested'] ?? 0);
        if ($assetValue <= 0) continue;
        $nonStableTotalValue += $assetValue;
    }
    if ($nonStableTotalValue <= 0) return null;

    // Market cap kategorilerini hesapla (stabilcoinler dahil değil)
    $largeCap = 0.0; // ≥10B USD
    $midCap = 0.0;   // 1B-10B USD
    $smallCap = 0.0; // <1B USD

    foreach ($context['assets'] as $asset) {
        if (is_asset_stablecoin($asset)) continue;
        $assetValue = isset($asset['currentValue']) ? (float)$asset['currentValue'] : (float)($asset['totalInvested'] ?? 0);
        if ($assetValue <= 0) continue;

        $assetWeight = $assetValue / $nonStableTotalValue;
        $marketCap = isset($asset['marketCap']) ? (float)$asset['marketCap'] : 0.0;

        if ($marketCap >= 10000000000) { // ≥10B
            $largeCap += $assetWeight;
        } elseif ($marketCap >= 1000000000) { // 1B-10B
            $midCap += $assetWeight;
        } else { // <1B
            $smallCap += $assetWeight;
        }
    }

    // Yüzdeye çevir
    return [
        'Large-Cap' => round($largeCap * 100, 2),
        'Mid-Cap' => round($midCap * 100, 2),
        'Small-Cap' => round($smallCap * 100, 2)
    ];
}

/**
 * Piyasa değeri dağılım skoru hesaplama
 * Large-Cap (≥10B), Mid-Cap (1B-10B), Small-Cap (<1B) dağılımını ideal aralıklarla karşılaştırır
 *
 * @param array|null $context
 * @return float|null 0-100 arası piyasa değeri dağılım skoru
 */
function compute_market_cap_distribution_score($context = null)
{
    if (!is_array($context) || !isset($context['assets']) || !is_array($context['assets'])) {
        return null;
    }

    // Stabilcoinleri hariç tutarak toplam değeri hesapla
    $nonStableTotalValue = 0.0;
    foreach ($context['assets'] as $asset) {
        if (is_asset_stablecoin($asset)) continue;
        $assetValue = isset($asset['currentValue']) ? (float)$asset['currentValue'] : (float)($asset['totalInvested'] ?? 0);
        if ($assetValue <= 0) continue;
        $nonStableTotalValue += $assetValue;
    }
    if ($nonStableTotalValue <= 0) return null;

    // Market cap kategorilerini hesapla (stabilcoinler dahil değil)
    $largeCap = 0.0; // ≥10B USD
    $midCap = 0.0;   // 1B-10B USD
    $smallCap = 0.0; // <1B USD

    foreach ($context['assets'] as $asset) {
        if (is_asset_stablecoin($asset)) continue;
        $assetValue = isset($asset['currentValue']) ? (float)$asset['currentValue'] : (float)($asset['totalInvested'] ?? 0);
        if ($assetValue <= 0) continue;

        $assetWeight = $assetValue / $nonStableTotalValue;
        $marketCap = isset($asset['marketCap']) ? (float)$asset['marketCap'] : 0.0;

        if ($marketCap >= 10000000000) { // ≥10B
            $largeCap += $assetWeight;
        } elseif ($marketCap >= 1000000000) { // 1B-10B
            $midCap += $assetWeight;
        } else { // <1B
            $smallCap += $assetWeight;
        }
    }

    // Yüzdeye çevir
    $largeCapPct = $largeCap * 100;
    $midCapPct = $midCap * 100;
    $smallCapPct = $smallCap * 100;

    $totalPenalty = 0.0;

    // Large-Cap: İdeal %30-60, her %5 sapma için 5 puan düşüş
    if ($largeCapPct < 30) {
        $deviation = 30 - $largeCapPct;
        $totalPenalty += ceil($deviation / 5) * 5;
    } elseif ($largeCapPct > 60) {
        $deviation = $largeCapPct - 60;
        $totalPenalty += ceil($deviation / 5) * 5;
    }

    // Mid-Cap: İdeal %15-30, her %5 sapma için 5 puan düşüş
    if ($midCapPct < 15) {
        $deviation = 15 - $midCapPct;
        $totalPenalty += ceil($deviation / 5) * 5;
    } elseif ($midCapPct > 30) {
        $deviation = $midCapPct - 30;
        $totalPenalty += ceil($deviation / 5) * 5;
    }

    // Small-Cap: İdeal %5-10, her %2 sapma için 5 puan düşüş
    if ($smallCapPct < 5) {
        $deviation = 5 - $smallCapPct;
        $totalPenalty += ceil($deviation / 2) * 5;
    } elseif ($smallCapPct > 10) {
        $deviation = $smallCapPct - 10;
        $totalPenalty += ceil($deviation / 2) * 5;
    }

    // Final skor: 100 - toplam ceza
    $score = 100 - $totalPenalty;

    return round(max(0.0, min(100.0, $score)), 2);
}

/**
 * Kategori dağılım yüzdelerini hesaplama (Frontend chart için)
 * Her kategorinin portföydeki yüzdelik ağırlığını döndürür
 *
 * @param array|null $context
 * @return array|null Kategori adı => yüzde değeri array'i
 */
function compute_category_distribution_percentages($context = null)
{
    if (!is_array($context) || !isset($context['assets']) || !is_array($context['assets'])) {
        return null;
    }

    $totalValue = isset($context['totalValue']) ? (float)$context['totalValue'] : 0.0;
    if ($totalValue <= 0) return null;

    // Kategori ağırlıklarını hesapla
    $categoryWeights = [];

    foreach ($context['assets'] as $asset) {
        $assetValue = isset($asset['currentValue']) ? (float)$asset['currentValue'] : (float)($asset['totalInvested'] ?? 0);
        if ($assetValue <= 0) continue;

        // Asset'in portföy ağırlığı
        $assetWeight = $assetValue / $totalValue;

        if (!isset($asset['categories']) || !is_array($asset['categories']) || empty($asset['categories'])) {
            continue;
        }

        // Bu asset'in kategorilerinin toplam market cap'ini hesapla
        $totalCategoryMarketCap = 0.0;
        foreach ($asset['categories'] as $category) {
            $categoryMarketCap = is_array($category) ? (float)($category['marketCap'] ?? 0) : 0.0;
            $totalCategoryMarketCap += $categoryMarketCap;
        }

        // Market cap oranlarına göre ağırlık dağıtımı
        foreach ($asset['categories'] as $category) {
            $categoryName = is_array($category) ? trim((string)($category['name'] ?? '')) : trim((string)$category);
            if ($categoryName === '') continue;

            $categoryMarketCap = is_array($category) ? (float)($category['marketCap'] ?? 0) : 0.0;
            $categoryRatio = $totalCategoryMarketCap > 0 ? ($categoryMarketCap / $totalCategoryMarketCap) : (1.0 / count($asset['categories']));
            $weightPerCategory = $assetWeight * $categoryRatio;

            if (!isset($categoryWeights[$categoryName])) {
                $categoryWeights[$categoryName] = 0.0;
            }
            $categoryWeights[$categoryName] += $weightPerCategory;
        }
    }

    if (empty($categoryWeights)) return null;

    // Yüzdeye çevir ve sırala
    $categoryPercentages = [];
    foreach ($categoryWeights as $category => $weight) {
        $categoryPercentages[$category] = round($weight * 100, 2);
    }

    // Büyükten küçüğe sırala
    arsort($categoryPercentages);

    return $categoryPercentages;
}

/**
 * Kategori çeşitlilik skoru hesaplama (HHI tabanlı)
 * HHI = Σ(kategori_ağırlığı^2), Çeşitlilik = (1 - HHI) * 100
 *
 * @param array|null $context
 * @return float|null 0-100 arası çeşitlilik skoru
 */
function compute_category_diversity_score($context = null)
{
    if (!is_array($context) || !isset($context['assets']) || !is_array($context['assets'])) {
        return null;
    }

    $totalValue = isset($context['totalValue']) ? (float)$context['totalValue'] : 0.0;
    if ($totalValue <= 0) return null;

    // Kategori ağırlıklarını hesapla
    $categoryWeights = [];

    foreach ($context['assets'] as $asset) {
        $assetValue = isset($asset['currentValue']) ? (float)$asset['currentValue'] : (float)($asset['totalInvested'] ?? 0);
        if ($assetValue <= 0) continue;

        // Asset'in portföy ağırlığı
        $assetWeight = $assetValue / $totalValue;

        if (!isset($asset['categories']) || !is_array($asset['categories']) || empty($asset['categories'])) {
            continue;
        }

        // Bu asset'in kategorilerinin toplam market cap'ini hesapla
        $totalCategoryMarketCap = 0.0;
        foreach ($asset['categories'] as $category) {
            $categoryMarketCap = is_array($category) ? (float)($category['marketCap'] ?? 0) : 0.0;
            $totalCategoryMarketCap += $categoryMarketCap;
        }

        // Market cap oranlarına göre ağırlık dağıtımı
        foreach ($asset['categories'] as $category) {
            $categoryName = is_array($category) ? trim((string)($category['name'] ?? '')) : trim((string)$category);
            if ($categoryName === '') continue;

            $categoryMarketCap = is_array($category) ? (float)($category['marketCap'] ?? 0) : 0.0;
            $categoryRatio = $totalCategoryMarketCap > 0 ? ($categoryMarketCap / $totalCategoryMarketCap) : (1.0 / count($asset['categories']));
            $weightPerCategory = $assetWeight * $categoryRatio;

            if (!isset($categoryWeights[$categoryName])) {
                $categoryWeights[$categoryName] = 0.0;
            }
            $categoryWeights[$categoryName] += $weightPerCategory;
        }
    }

    if (empty($categoryWeights)) return null;

    // HHI hesapla: Σ(ağırlık^2)
    $hhi = 0.0;
    foreach ($categoryWeights as $weight) {
        $hhi += $weight * $weight;
    }

    // Çeşitlilik skoru: (1 - HHI) * 100
    $diversityScore = (1.0 - $hhi) * 100.0;

    return round(max(0.0, min(100.0, $diversityScore)), 2);
}

/**
 * Ağırlıklı kategori skorları hesaplama
 * Her asset'in portföy ağırlığı × kategori market cap ağırlığı × trend skoru
 *
 * @param array|null $context
 * @return array{"score":float,"categories":array<string,float>,"distinctCount":int}|null
 */
function compute_category_scores($context = null)
{
    if (!is_array($context) || !isset($context['assets']) || !is_array($context['assets'])) {
        return null;
    }

    $totalValue = isset($context['totalValue']) ? (float)$context['totalValue'] : 0.0;
    if ($totalValue <= 0) return null;

    // Get trend scores for all categories
    $trendData = compute_categories_trend_components($context);
    if (empty($trendData['categories'])) return null;

    // Get all unique categories from portfolio
    $allCategories = [];
    foreach ($context['assets'] as $asset) {
        if (isset($asset['categories']) && is_array($asset['categories'])) {
            foreach ($asset['categories'] as $cat) {
                $catName = is_array($cat) ? trim((string)($cat['name'] ?? '')) : trim((string)$cat);
                if ($catName !== '') {
                    $allCategories[$catName] = true;
                }
            }
        }
    }
    $categoryNames = array_keys($allCategories);

    // Get market caps for categories
    $categoryMarketCaps = fetch_category_market_caps($categoryNames);

    $weightedScore = 0.0;

    foreach ($context['assets'] as $asset) {
        $assetValue = isset($asset['currentValue']) ? (float)$asset['currentValue'] : (float)($asset['totalInvested'] ?? 0);
        if ($assetValue <= 0) continue;

        // Asset'in portföy ağırlığı
        $assetWeight = $assetValue / $totalValue;

        if (!isset($asset['categories']) || !is_array($asset['categories']) || empty($asset['categories'])) {
            continue;
        }

        // Bu asset'in kategorilerinin toplam market cap'i
        $totalCategoryMarketCap = 0.0;
        foreach ($asset['categories'] as $cat) {
            $catName = is_array($cat) ? trim((string)($cat['name'] ?? '')) : trim((string)$cat);
            $totalCategoryMarketCap += isset($categoryMarketCaps[$catName]) ? $categoryMarketCaps[$catName] : 0.0;
        }

        if ($totalCategoryMarketCap <= 0) continue;

        // Her kategori için ağırlıklı skor hesapla
        foreach ($asset['categories'] as $cat) {
            $catName = is_array($cat) ? trim((string)($cat['name'] ?? '')) : trim((string)$cat);
            $categoryMarketCap = isset($categoryMarketCaps[$catName]) ? $categoryMarketCaps[$catName] : 0.0;
            $categoryTrendScore = isset($trendData['categories'][$catName]) ? $trendData['categories'][$catName] : 0.0;

            // Kategori ağırlığı (market cap'e göre)
            $categoryWeight = $totalCategoryMarketCap > 0 ? ($categoryMarketCap / $totalCategoryMarketCap) : 0.0;

            // Final ağırlık = asset ağırlığı × kategori ağırlığı
            $finalWeight = $assetWeight * $categoryWeight;

            // Ağırlıklı skora ekle
            $weightedScore += $finalWeight * $categoryTrendScore;
        }
    }

    return [
        'score' => round($weightedScore, 2),
        'categories' => $trendData['categories'],
        'distinctCount' => count($categoryNames)
    ];
}


/**
 * Portföydeki kategorileri, o kategoriye ait coinlerin toplam allocation oranlarına göre gruplar
 * Not: Bir coin birden fazla kategoride olabileceği için kategori allocation toplamı %100'ü aşabilir.
 *
 * Çıkış formatı:
 * [
 *   {
 *     name: string,
 *     allocation: float, // kategori altındaki coin allocation toplamı
 *     coins: [ { assetId, coinId, symbol, name, logoUrl, allocation }, ... ]
 *   },
 *   ...
 * ]
 *
 * @param array|null $context Portfolio context (assets, totalValue, totalInvested)
 * @return array Trending categories list
 */
function compute_trending_categories($context = null)
{
    if (!is_array($context) || !isset($context['assets']) || !is_array($context['assets'])) {
        return [];
    }

    $assets = $context['assets'];
    $totalValue = isset($context['totalValue']) ? (float)$context['totalValue'] : 0.0;
    $totalInvested = isset($context['totalInvested']) ? (float)$context['totalInvested'] : 0.0;
    $denominator = $totalValue > 0 ? $totalValue : ($totalInvested > 0 ? $totalInvested : 0.0);

    $byCategory = [];

    foreach ($assets as $asset) {
        // Asset allocation yüzdesi (hazırsa kullan, yoksa hesapla)
        $alloc = null;
        if (isset($asset['allocation']) && $asset['allocation'] !== null) {
            $alloc = (float)$asset['allocation'];
        } else {
            $basis = isset($asset['currentValue']) && $asset['currentValue'] !== null
                ? (float)$asset['currentValue']
                : (float)($asset['totalInvested'] ?? 0);
            $alloc = $denominator > 0 ? round(($basis / $denominator) * 100, 2) : 0.0;
        }

        if (!isset($asset['categories']) || !is_array($asset['categories']) || empty($asset['categories'])) {
            continue;
        }

        foreach ($asset['categories'] as $cat) {
            $catName = is_array($cat) ? trim((string)($cat['name'] ?? '')) : trim((string)$cat);
            if ($catName === '') continue;

            if (!isset($byCategory[$catName])) {
                $byCategory[$catName] = [
                    'name' => $catName,
                    'allocation' => 0.0,
                    'coins' => []
                ];
            }

            // Bu kategorinin toplam allocation'ına asset allocation'ını TAM olarak ekle
            $byCategory[$catName]['allocation'] += $alloc;

            // Coin bilgisini ekle (minimal alanlar)
            $byCategory[$catName]['coins'][] = [
                'assetId' => isset($asset['id']) ? (string)$asset['id'] : null,
                'coinId' => isset($asset['coinId']) ? (string)$asset['coinId'] : null,
                'symbol' => $asset['symbol'] ?? null,
                'name' => $asset['name'] ?? null,
                'logoUrl' => $asset['logoUrl'] ?? null,
                'allocation' => $alloc
            ];
        }
    }

    if (empty($byCategory)) return [];

    // Diziye çevir, allocation'ları yuvarla ve coinleri allocation'a göre sırala
    $result = array_values($byCategory);
    foreach ($result as &$entry) {
        $entry['allocation'] = round((float)$entry['allocation'], 2);
        if (isset($entry['coins']) && is_array($entry['coins'])) {
            usort($entry['coins'], function ($a, $b) {
                $aa = isset($a['allocation']) ? (float)$a['allocation'] : 0.0;
                $bb = isset($b['allocation']) ? (float)$b['allocation'] : 0.0;
                if ($aa === $bb) return 0;
                return ($aa > $bb) ? -1 : 1;
            });
        }
    }
    unset($entry);

    // Kategorileri de allocation azalansına göre sırala
    usort($result, function ($a, $b) {
        $aa = isset($a['allocation']) ? (float)$a['allocation'] : 0.0;
        $bb = isset($b['allocation']) ? (float)$b['allocation'] : 0.0;
        if ($aa === $bb) return 0;
        return ($aa > $bb) ? -1 : 1;
    });

    return $result;
}


/**
 * Stablecoin oranı detaylarını hesaplar ve detaylı bir analiz objesi döndürür.
 *
 * @param array|null $context assets, totalValue, totalInvested içeren context
 * @return array Stablecoin ratio analysis
 */
function compute_stablecoin_ratio_details($context = null)
{
    // Varsayılan boş yapı
    $empty = [
        'stablecoin_analysis' => [
            'current_ratio' => 0.0,
            'current_value' => 0.0,
            'target_ratio' => 25.0,
            'target_value' => 0.0,
            'recommended_range' => [ 'min' => 20.0, 'max' => 25.0 ],
            'stablecoins' => [],
            'status' => 'needs_more_stability',
            'risk_level' => 'medium',
            'recommendations' => [],
            'score' => 0,
            'score_breakdown' => [
                'ratio_optimality' => 0,
                'diversification' => 0,
                'protocol_risk' => 0
            ]
        ]
    ];

    if (!is_array($context) || !isset($context['assets']) || !is_array($context['assets'])) {
        return $empty;
    }

    $assets = $context['assets'];
    $totalValue = isset($context['totalValue']) ? (float)$context['totalValue'] : 0.0;
    if ($totalValue <= 0) {
        return $empty;
    }

    // Stabilcoinleri topla
    $stablecoinValue = 0.0;
    $stablecoinEntries = [];

    // Denominator for allocations when not present
    $denominator = $totalValue;

    foreach ($assets as $asset) {
        if (!is_asset_stablecoin($asset)) continue;

        $value = isset($asset['currentValue']) && $asset['currentValue'] !== null
            ? (float)$asset['currentValue']
            : (float)($asset['totalInvested'] ?? 0);
        if ($value <= 0) continue;

        $stablecoinValue += $value;

        // Allocation yüzdesi hazırsa kullan, değilse hesapla
        $alloc = isset($asset['allocation']) && $asset['allocation'] !== null
            ? (float)$asset['allocation']
            : ($denominator > 0 ? round(($value / $denominator) * 100, 2) : 0.0);

        // Protokol çıkarımı: kategori isimlerinden "ecosystem" içeren ilkini al
        $protocol = null;
        if (isset($asset['categories']) && is_array($asset['categories'])) {
            foreach ($asset['categories'] as $cat) {
                $nm = is_array($cat) ? (string)($cat['name'] ?? '') : (string)$cat;
                if ($nm !== '' && stripos($nm, 'ecosystem') !== false) {
                    $protocol = $nm;
                    break;
                }
            }
        }

        $stablecoinEntries[] = [
            'symbol' => $asset['symbol'] ?? null,
            'amount' => isset($asset['amount']) ? (float)$asset['amount'] : null,
            'value' => round($value, 2),
            'allocation' => $alloc,
            'protocol' => $protocol
        ];
    }

    $currentRatio = $stablecoinValue / $totalValue; // 0-1
    $currentRatioPct = round($currentRatio * 100, 2);

    // Hedef ve öneriler
    $rangeMin = 20.0; // %
    $rangeMax = 25.0; // %
    $targetRatioPct = ($currentRatioPct < $rangeMin) ? $rangeMin : (($currentRatioPct > $rangeMax) ? $rangeMax : $currentRatioPct);
    $targetValue = round(($targetRatioPct / 100.0) * $totalValue, 2);

    // Durum ve risk
    if ($currentRatioPct < $rangeMin) {
        $status = 'needs_more_stability';
    } elseif ($currentRatioPct > $rangeMax) {
        $status = 'overexposed';
    } else {
        $status = 'well_balanced';
    }

    // Skor ve dağılım
    $ratioScore = compute_stablecoin_ratio_score($context);
    if ($ratioScore === null) $ratioScore = 0;

    // Çeşitlilik skoru: farklı stablecoin sayısına göre
    $distinctSymbols = [];
    $distinctProtocols = [];
    foreach ($stablecoinEntries as $e) {
        if (!empty($e['symbol'])) $distinctSymbols[$e['symbol']] = true;
        if (!empty($e['protocol'])) $distinctProtocols[$e['protocol']] = true;
    }
    $symCount = count($distinctSymbols);
    $protoCount = count($distinctProtocols);

    $diversificationScore = 0;
    if ($symCount >= 3) $diversificationScore = 90;
    elseif ($symCount === 2) $diversificationScore = 75;
    elseif ($symCount === 1) $diversificationScore = 60;

    // Protokol risk skoru: çoklu protokol -> daha yüksek skor
    $protocolRiskScore = 0;
    if ($protoCount >= 3) $protocolRiskScore = 90;
    elseif ($protoCount === 2) $protocolRiskScore = 75;
    elseif ($protoCount === 1) $protocolRiskScore = 60;

    // Basit ortalama ile genel skor
    $overallScore = (int)round(($ratioScore + $diversificationScore + $protocolRiskScore) / 3.0);

    // Öneriler
    $recs = [];
    if ($status === 'needs_more_stability') {
        $recs[] = "Stablecoin oranı önerilen aralığın altında. %{$rangeMin} seviyesine yükseltmeyi değerlendirin.";
    } elseif ($status === 'overexposed') {
        $recs[] = "Stablecoin oranı önerilen aralığın üzerinde. %{$rangeMax} seviyesine düşürmeyi değerlendirin.";
    } else {
        $recs[] = "Stablecoin oranı dengeli görünüyor. Mevcut dengeyi koruyun.";
    }
    if ($symCount <= 1) {
        $recs[] = "Tek bir stablecoin'e yoğunlaşmış. USDT/USDC/DAI gibi birden fazla varlığa dağıtımı düşünün.";
    }
    if ($protoCount <= 1) {
        $recs[] = "Tek bir ekosisteme bağlı görünüyorsunuz. Çoklu ekosistem (Ethereum, Tron, Solana vb.) dağılımı riski azaltabilir.";
    }

    // Stablecoinleri allocation azalan sırada sırala
    usort($stablecoinEntries, function ($a, $b) {
        $aa = isset($a['allocation']) ? (float)$a['allocation'] : 0.0;
        $bb = isset($b['allocation']) ? (float)$b['allocation'] : 0.0;
        if ($aa === $bb) return 0;
        return ($aa > $bb) ? -1 : 1;
    });

    return [
        'stablecoin_analysis' => [
            'current_ratio' => $currentRatioPct,
            'current_value' => round($stablecoinValue, 2),
            'target_ratio' => $targetRatioPct,
            'target_value' => $targetValue,
            'recommended_range' => [ 'min' => $rangeMin, 'max' => $rangeMax ],
            'stablecoins' => $stablecoinEntries,
            'status' => $status,
            'risk_level' => ($overallScore >= 85 ? 'low' : ($overallScore >= 70 ? 'medium' : 'high')),
            'recommendations' => $recs,
            'score' => $overallScore,
            'score_breakdown' => [
                'ratio_optimality' => (int)$ratioScore,
                'diversification' => (int)$diversificationScore,
                'protocol_risk' => (int)$protocolRiskScore
            ]
        ]
    ];
}
