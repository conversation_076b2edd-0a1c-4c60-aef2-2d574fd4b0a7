<?php

require_once __DIR__ . '/utils.php';
require_once __DIR__ . '/models/ResultModel.php';
require_once __DIR__ . '/config.php';

cors_client();
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    (new ErrorResult('Method not allowed'))->send(405);
    return;
}

$payload = file_get_contents('php://input');
$data = json_decode($payload, true);

if ($data === null) {
    (new ErrorResult('Invalid Request!'))->send(400);
    return;
}

$functName = $data['f'] ?? null;
if ($functName === null || $functName === '') {
    (new ErrorResult('Invalid Request!'))->send(404);
    return;
}

$userId = authenticate_user();
if (!$userId) {
    (new ErrorResult('Unauthorized access'))->send(401);
    return;
}

$validEndpoints = ['get_conversations', 'get_conversation_messages', 'get_help_conversations', 'get_help_conversation_messages'];

if (!in_array($functName, $validEndpoints, true)) {
    (new ErrorResult('Invalid Request!'))->send(404);
    return;
}

switch ($functName) {
    case 'get_conversations': {
        try {
            global $link;

            $stmt = mysqli_prepare($link, "SELECT uuid as id, name, is_closed, created_at FROM chat_conversations WHERE user_id = ? ORDER BY created_at DESC");
            mysqli_stmt_bind_param($stmt, "i", $userId);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $conversations = mysqli_fetch_all($result, MYSQLI_ASSOC);
            mysqli_stmt_close($stmt);

            (new SuccessResult(['conversations' => $conversations]))->send(200);
            return;

        } catch (Exception $e) {
            (new ErrorResult('Internal server error'))->send(500);
            return;
        }
    }

    case 'get_conversation_messages': {
    $conversationUuid = isset($data['conversation_id']) ? $data['conversation_id'] : null;

    if (!$conversationUuid) {
        (new ErrorResult('conversation_id is required'))->send(400);
        return;
    }

    try {
        global $link;

        // Konuşmanın kullanıcıya ait olduğunu ve durumunu kontrol et
        $stmt = mysqli_prepare($link, "SELECT id, is_closed FROM chat_conversations WHERE uuid = ? AND user_id = ?");
        mysqli_stmt_bind_param($stmt, "si", $conversationUuid, $userId);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);

        if (mysqli_num_rows($result) === 0) {
            mysqli_stmt_close($stmt);
            (new ErrorResult('Conversation not found'))->send(404);
            return;
        }

        $conv = mysqli_fetch_assoc($result);
        $realId = $conv['id']; // chat_messages için kullanacağımız gerçek ID
        mysqli_stmt_close($stmt);

        // Mesajları çek
        $stmt = mysqli_prepare($link, "SELECT sender, message_text, message_type, template_data, created_at FROM chat_messages WHERE conversation_id = ? ORDER BY created_at ASC");
        mysqli_stmt_bind_param($stmt, "i", $realId);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $rows = mysqli_fetch_all($result, MYSQLI_ASSOC);
        mysqli_stmt_close($stmt);

        // Client'in beklediği formatta templatedata'yı ekle
        $messages = [];
        foreach ($rows as $row) {
            $msg = [
                'sender' => $row['sender'],
                'message_text' => $row['message_text'],
                'created_at' => $row['created_at']
            ];
            if (isset($row['message_type']) && $row['message_type'] === 'template') {
                if (!empty($row['template_data'])) {
                    $tpl = $row['template_data'];
                    if (is_string($tpl)) {
                        $decoded = json_decode($tpl, true);
                        if (json_last_error() === JSON_ERROR_NONE) {
                            $tpl = $decoded;
                        }
                    }
                    $msg['templateData'] = $tpl;
                }
            }
            $messages[] = $msg;
        }

        // Artık uuid gönderiyoruz çünkü client bunu biliyor
        (new SuccessResult([
            'messages' => $messages,
            'conversation' => [
                'id' => $conversationUuid,
                'is_closed' => (bool)$conv['is_closed']
            ]
        ]))->send(200);
        return;

    } catch (Exception $e) {
        (new ErrorResult('Internal server error'))->send(500);
        return;
    }
}

    case 'get_help_conversations': {
        try {
            global $link;

            $stmt = mysqli_prepare($link, "SELECT id, created_at FROM help_conversations WHERE user_id = ? ORDER BY created_at DESC");
            mysqli_stmt_bind_param($stmt, "i", $userId);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $conversations = mysqli_fetch_all($result, MYSQLI_ASSOC);
            mysqli_stmt_close($stmt);

            (new SuccessResult(['conversations' => $conversations]))->send(200);
            return;

        } catch (Exception $e) {
            (new ErrorResult('Internal server error'))->send(500);
            return;
        }
    }

    case 'get_help_conversation_messages': {
        $conversationId = isset($data['conversation_id']) ? (int)$data['conversation_id'] : null;

        if (!$conversationId) {
            (new ErrorResult('conversation_id is required'))->send(400);
            return;
        }

        try {
            global $link;

            // Konuşmanın kullanıcıya ait olduğunu kontrol et
            $stmt = mysqli_prepare($link, "SELECT id FROM help_conversations WHERE id = ? AND user_id = ?");
            mysqli_stmt_bind_param($stmt, "ii", $conversationId, $userId);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);

            if (mysqli_num_rows($result) === 0) {
                mysqli_stmt_close($stmt);
                (new ErrorResult('Conversation not found'))->send(404);
                return;
            }
            mysqli_stmt_close($stmt);

            // Mesajları çek
            $stmt = mysqli_prepare($link, "SELECT sender, message_text, created_at FROM help_messages WHERE conversation_id = ? ORDER BY created_at ASC");
            mysqli_stmt_bind_param($stmt, "i", $conversationId);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $messages = mysqli_fetch_all($result, MYSQLI_ASSOC);
            mysqli_stmt_close($stmt);

            (new SuccessResult(['messages' => $messages]))->send(200);
            return;

        } catch (Exception $e) {
            (new ErrorResult('Internal server error'))->send(500);
            return;
        }
    }
}