-- Create stripe_subscription_cooldown_config table to store cooldown configuration
CREATE TABLE IF NOT EXISTS stripe_subscription_cooldown_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    change_type VARCHAR(50) NOT NULL, -- 'upgrade', 'downgrade', 'any'
    from_level VARCHAR(50) NOT NULL, -- 'free', 'basic', 'advance', 'premium', 'any'
    to_level VARCHAR(50) NOT NULL, -- 'free', 'basic', 'advance', 'premium', 'any'
    cooldown_days INT NOT NULL DEFAULT 30, -- Number of days before allowing another change
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_change_config (change_type, from_level, to_level)
);

-- Insert default cooldown configurations with fixed values
-- These values can be modified directly in the database if needed
INSERT INTO stripe_subscription_cooldown_config
    (change_type, from_level, to_level, cooldown_days)
VALUES
    ('downgrade', 'any', 'any', 30), -- 30 days cooldown for any downgrade
    ('upgrade', 'any', 'any', 7),    -- 7 days cooldown for any upgrade
    ('any', 'any', 'any', 15);       -- 15 days default cooldown for any change not covered by specific rules

-- Specific rules can be added if needed, for example:
-- INSERT INTO stripe_subscription_cooldown_config (change_type, from_level, to_level, cooldown_days)
-- VALUES ('upgrade', 'basic', 'premium', 0); -- Allow immediate upgrade from basic to premium
