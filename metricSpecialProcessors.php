<?php

/**
 * MetricSpecialProcessors.php
 *
 * Bu dosya özel metric verilerini işlemek için kullanılan fonksiyonları içerir.
 * Her metric ID için farklı veri yapıları ve işleme mantıkları bulunur.
 */

/**
 * Metric ID 36 için def_or_inf JSON verisini işler ve anlamlı bir obje döndürür
 *
 * @param string $jsonData JSON formatında use case verisi
 * @return array İşlenmiş use case bilgileri
 */
function processUseCaseData($jsonData)
{
    if (empty($jsonData)) {
        return [
            'use_cases' => [],
            'total_use_cases' => 0,
            'summary' => 'No use case data available'
        ];
    }

    // JSON verisini decode et
    $data = json_decode($jsonData, true);

    if (!is_array($data) || empty($data)) {
        return [
            'use_cases' => [],
            'total_use_cases' => 0,
            'summary' => 'Invalid use case data'
        ];
    }

    $processedUseCases = [];
    $useCaseTypes = [];

    foreach ($data as $record) {
        if (isset($record['metric_subtype']) && isset($record['source_text'])) {
            $useCase = [
                'type' => $record['metric_subtype'],
                'description' => $record['source_text'],
                'analysis' => $record['check_reason'] ?? '',
                'check_status' => $record['check'] ?? '0',
                'date' => $record['date'] ?? ''
            ];

            $processedUseCases[] = $useCase;

            // Use case türlerini say
            $type = $record['metric_subtype'];
            if (!isset($useCaseTypes[$type])) {
                $useCaseTypes[$type] = 0;
            }
            $useCaseTypes[$type]++;
        }
    }

    // Özet bilgi oluştur
    $summary = '';
    if (!empty($useCaseTypes)) {
        $typeNames = array_keys($useCaseTypes);
        if (count($typeNames) == 1) {
            $summary = "Primary use case: " . $typeNames[0];
        } else {
            $summary = "Multiple use cases including: " . implode(', ', array_slice($typeNames, 0, 3));
            if (count($typeNames) > 3) {
                $summary .= " and " . (count($typeNames) - 3) . " more";
            }
        }
    }

    return [
        'use_cases' => $processedUseCases,
        'total_use_cases' => count($processedUseCases),
        'use_case_types' => $useCaseTypes,
        'summary' => $summary
    ];
}

/**
 * Metric ID 38 için def_or_inf JSON verisini işler ve anlamlı bir obje döndürür
 *
 * @param string $jsonData JSON formatında deflationary/inflationary verisi
 * @return array İşlenmiş tokenomics bilgileri
 */
function processTokenomicsData($jsonData)
{
    if (empty($jsonData)) {
        return [
            'mechanisms' => [],
            'total_mechanisms' => 0,
            'mechanism_types' => [],
            'overall_trend' => 'Unknown',
            'summary' => 'No tokenomics data available'
        ];
    }

    // JSON verisini decode et
    $data = json_decode($jsonData, true);

    if (!is_array($data) || empty($data)) {
        return [
            'mechanisms' => [],
            'total_mechanisms' => 0,
            'mechanism_types' => [],
            'overall_trend' => 'Unknown',
            'summary' => 'Invalid tokenomics data'
        ];
    }

    $processedMechanisms = [];
    $mechanismTypes = [];
    $burnMechanisms = 0;
    $buybackMechanisms = 0;

    foreach ($data as $record) {
        if (isset($record['metric_subtype']) && isset($record['source_text'])) {
            $mechanism = [
                'type' => $record['metric_subtype'],
                'description' => $record['source_text'],
                'analysis' => $record['check_reason'] ?? '',
                'check_status' => $record['check'] ?? '0',
                'date' => $record['date'] ?? ''
            ];

            $processedMechanisms[] = $mechanism;

            // Mechanism türlerini say ve kategorize et
            $type = $record['metric_subtype'];
            if (!isset($mechanismTypes[$type])) {
                $mechanismTypes[$type] = 0;
            }
            $mechanismTypes[$type]++;

            // Burn ve buyback mekanizmalarını say
            if (stripos($type, 'burn') !== false) {
                $burnMechanisms++;
            }
            if (stripos($type, 'buyback') !== false) {
                $buybackMechanisms++;
            }
        }
    }

    // Genel trend belirle
    $overallTrend = 'Neutral';
    if ($burnMechanisms > 0 || $buybackMechanisms > 0) {
        $overallTrend = 'Deflationary';
    }

    // Özet bilgi oluştur
    $summary = '';
    if (!empty($mechanismTypes)) {
        $totalMechanisms = count($processedMechanisms);
        $uniqueTypes = count($mechanismTypes);

        if ($overallTrend === 'Deflationary') {
            $summary = "Deflationary tokenomics with {$totalMechanisms} mechanism(s)";
            if ($burnMechanisms > 0) {
                $summary .= " including {$burnMechanisms} burn mechanism(s)";
            }
            if ($buybackMechanisms > 0) {
                $summary .= " and {$buybackMechanisms} buyback mechanism(s)";
            }
        } else {
            $summary = "{$totalMechanisms} tokenomics mechanism(s) across {$uniqueTypes} type(s)";
        }
    }

    return [
        'mechanisms' => $processedMechanisms,
        'total_mechanisms' => count($processedMechanisms),
        'mechanism_types' => $mechanismTypes,
        'burn_mechanisms' => $burnMechanisms,
        'buyback_mechanisms' => $buybackMechanisms,
        'overall_trend' => $overallTrend,
        'summary' => $summary
    ];
}

/**
 * Metric ID 50 için team_anonymity JSON verisini işler ve anlamlı bir obje döndürür
 *
 * @param string $jsonData JSON formatında team anonymity verisi
 * @return array İşlenmiş team bilgileri
 */
function processTeamAnonymityData($jsonData)
{
    if (empty($jsonData)) {
        return [
            'status' => 'Unknown',
            'team_members' => [],
            'total_members' => 0
        ];
    }

    // JSON verisini decode et
    $data = json_decode($jsonData, true);

    if (!is_array($data) || empty($data)) {
        return [
            'status' => 'Unknown',
            'team_members' => [],
            'total_members' => 0
        ];
    }

    $firstRecord = $data[0];
    $status = $firstRecord['source_text'] ?? 'Unknown';
    $checkReason = $firstRecord['check_reason'] ?? '[]';

    // check_reason JSON'ını decode et
    $teamMembers = json_decode($checkReason, true);

    if (!is_array($teamMembers)) {
        $teamMembers = [];
    }

    // Team üyelerini işle - sadece Name ve Title bilgilerini al
    $processedMembers = [];
    foreach ($teamMembers as $member) {
        if (isset($member['Name']) && isset($member['Title'])) {
            $processedMembers[] = [
                'name' => $member['Name'],
                'title' => $member['Title']
            ];
        }
    }

    return [
        'status' => $status,
        'team_members' => $processedMembers,
        'total_members' => count($processedMembers)
    ];
}

/**
 * Metric ID 40 için token_redist_score JSON verisini işler ve anlamlı bir obje döndürür
 *
 * @param string $jsonData JSON formatında token redistribution verisi
 * @return array İşlenmiş token redistribution bilgileri
 */
function processTokenRedistributionData($jsonData)
{
    if (empty($jsonData)) {
        return [
            'redistribution_mechanisms' => [],
            'total_mechanisms' => 0,
            'mechanism_types' => [],
            'redistribution_score' => 'Unknown',
            'summary' => 'No token redistribution data available'
        ];
    }

    // JSON verisini decode et
    $data = json_decode($jsonData, true);

    if (!is_array($data) || empty($data)) {
        return [
            'redistribution_mechanisms' => [],
            'total_mechanisms' => 0,
            'mechanism_types' => [],
            'redistribution_score' => 'Unknown',
            'summary' => 'Invalid token redistribution data'
        ];
    }

    $processedMechanisms = [];
    $mechanismTypes = [];
    $stakingMechanisms = 0;
    $feeSharingMechanisms = 0;
    $rewardMechanisms = 0;
    $burnMechanisms = 0;

    foreach ($data as $record) {
        if (isset($record['metric_subtype']) && isset($record['source_text'])) {
            $mechanism = [
                'type' => $record['metric_subtype'],
                'description' => $record['source_text'],
                'analysis' => $record['check_reason'] ?? '',
                'check_status' => $record['check'] ?? '0',
                'date' => $record['date'] ?? ''
            ];

            $processedMechanisms[] = $mechanism;

            // Mechanism türlerini say ve kategorize et
            $type = strtolower($record['metric_subtype']);
            if (!isset($mechanismTypes[$record['metric_subtype']])) {
                $mechanismTypes[$record['metric_subtype']] = 0;
            }
            $mechanismTypes[$record['metric_subtype']]++;

            // Farklı redistribution mekanizmalarını say
            if (stripos($type, 'staking') !== false || stripos($type, 'stake') !== false) {
                $stakingMechanisms++;
            }
            if (stripos($type, 'fee') !== false || stripos($type, 'transaction fee') !== false) {
                $feeSharingMechanisms++;
            }
            if (stripos($type, 'reward') !== false || stripos($type, 'block') !== false) {
                $rewardMechanisms++;
            }
            if (stripos($type, 'burn') !== false) {
                $burnMechanisms++;
            }
        }
    }

    // Redistribution score belirle
    $redistributionScore = 'Low';
    $totalMechanisms = count($processedMechanisms);

    if ($totalMechanisms >= 3) {
        $redistributionScore = 'High';
    } elseif ($totalMechanisms >= 2) {
        $redistributionScore = 'Medium';
    } elseif ($totalMechanisms >= 1) {
        $redistributionScore = 'Low';
    } else {
        $redistributionScore = 'None';
    }

    // Özet bilgi oluştur
    $summary = '';
    if (!empty($mechanismTypes)) {
        $uniqueTypes = count($mechanismTypes);
        $summary = "{$totalMechanisms} redistribution mechanism(s) across {$uniqueTypes} type(s)";

        $mechanismDetails = [];
        if ($stakingMechanisms > 0) {
            $mechanismDetails[] = "{$stakingMechanisms} staking mechanism(s)";
        }
        if ($feeSharingMechanisms > 0) {
            $mechanismDetails[] = "{$feeSharingMechanisms} fee sharing mechanism(s)";
        }
        if ($rewardMechanisms > 0) {
            $mechanismDetails[] = "{$rewardMechanisms} reward mechanism(s)";
        }
        if ($burnMechanisms > 0) {
            $mechanismDetails[] = "{$burnMechanisms} burn mechanism(s)";
        }

        if (!empty($mechanismDetails)) {
            $summary .= " including " . implode(', ', $mechanismDetails);
        }
    }

    return [
        'redistribution_mechanisms' => $processedMechanisms,
        'total_mechanisms' => count($processedMechanisms),
        'mechanism_types' => $mechanismTypes,
        'staking_mechanisms' => $stakingMechanisms,
        'fee_sharing_mechanisms' => $feeSharingMechanisms,
        'reward_mechanisms' => $rewardMechanisms,
        'burn_mechanisms' => $burnMechanisms,
        'redistribution_score' => $redistributionScore,
        'summary' => $summary
    ];
}

/**
 * Metric ID 41 için token_buyback JSON verisini işler ve anlamlı bir obje döndürür
 *
 * @param string $jsonData JSON formatında token buyback verisi
 * @return array İşlenmiş token buyback bilgileri
 */
function processTokenBuybackData($jsonData)
{
    if (empty($jsonData)) {
        return [
            'buyback_mechanisms' => [],
            'total_mechanisms' => 0,
            'mechanism_types' => [],
            'buyback_score' => 'Unknown',
            'has_burn_component' => false,
            'revenue_based' => false,
            'summary' => 'No token buyback data available'
        ];
    }

    // JSON verisini decode et
    $data = json_decode($jsonData, true);

    if (!is_array($data) || empty($data)) {
        return [
            'buyback_mechanisms' => [],
            'total_mechanisms' => 0,
            'mechanism_types' => [],
            'buyback_score' => 'Unknown',
            'has_burn_component' => false,
            'revenue_based' => false,
            'summary' => 'Invalid token buyback data'
        ];
    }

    $processedMechanisms = [];
    $mechanismTypes = [];
    $revenueBuybacks = 0;
    $burnBuybacks = 0;
    $hasBurnComponent = false;
    $isRevenueBased = false;

    foreach ($data as $record) {
        if (isset($record['metric_subtype']) && isset($record['source_text'])) {
            $mechanism = [
                'type' => $record['metric_subtype'],
                'description' => $record['source_text'],
                'analysis' => $record['check_reason'] ?? '',
                'check_status' => $record['check'] ?? '0',
                'date' => $record['date'] ?? ''
            ];

            $processedMechanisms[] = $mechanism;

            // Mechanism türlerini say ve kategorize et
            $type = strtolower($record['metric_subtype']);
            $description = strtolower($record['source_text']);
            $analysis = strtolower($record['check_reason'] ?? '');

            if (!isset($mechanismTypes[$record['metric_subtype']])) {
                $mechanismTypes[$record['metric_subtype']] = 0;
            }
            $mechanismTypes[$record['metric_subtype']]++;

            // Revenue-based buyback kontrolü
            if (stripos($type, 'revenue') !== false ||
                stripos($description, 'revenue') !== false ||
                stripos($description, 'profit') !== false ||
                stripos($analysis, 'revenue') !== false ||
                stripos($analysis, 'profit') !== false) {
                $revenueBuybacks++;
                $isRevenueBased = true;
            }

            // Burn component kontrolü
            if (stripos($description, 'burn') !== false ||
                stripos($analysis, 'burn') !== false ||
                stripos($description, 'destroy') !== false ||
                stripos($analysis, 'destroy') !== false) {
                $burnBuybacks++;
                $hasBurnComponent = true;
            }
        }
    }

    // Buyback score belirle
    $buybackScore = 'Low';
    $totalMechanisms = count($processedMechanisms);

    // Score hesaplama: base score + bonuses
    $score = $totalMechanisms;
    if ($isRevenueBased) $score += 1;  // Revenue-based buybacks are stronger
    if ($hasBurnComponent) $score += 1; // Burn component adds deflationary pressure

    if ($score >= 4) {
        $buybackScore = 'Excellent';
    } elseif ($score >= 3) {
        $buybackScore = 'High';
    } elseif ($score >= 2) {
        $buybackScore = 'Medium';
    } elseif ($score >= 1) {
        $buybackScore = 'Low';
    } else {
        $buybackScore = 'None';
    }

    // Özet bilgi oluştur
    $summary = '';
    if (!empty($mechanismTypes)) {
        $uniqueTypes = count($mechanismTypes);
        $summary = "{$totalMechanisms} buyback mechanism(s) across {$uniqueTypes} type(s)";

        $features = [];
        if ($isRevenueBased) {
            $features[] = "revenue-based";
        }
        if ($hasBurnComponent) {
            $features[] = "with burn component";
        }

        if (!empty($features)) {
            $summary .= " (" . implode(', ', $features) . ")";
        }

        if ($revenueBuybacks > 0) {
            $summary .= ". {$revenueBuybacks} revenue-driven buyback(s)";
        }
        if ($burnBuybacks > 0) {
            $summary .= ". {$burnBuybacks} buyback(s) include token burning";
        }
    }

    return [
        'buyback_mechanisms' => $processedMechanisms,
        'total_mechanisms' => count($processedMechanisms),
        'mechanism_types' => $mechanismTypes,
        'revenue_buybacks' => $revenueBuybacks,
        'burn_buybacks' => $burnBuybacks,
        'buyback_score' => $buybackScore,
        'has_burn_component' => $hasBurnComponent,
        'revenue_based' => $isRevenueBased,
        'summary' => $summary
    ];
}

/**
 * Metric ID 52 için dao_governance JSON verisini işler ve anlamlı bir obje döndürür
 *
 * @param string $jsonData JSON formatında DAO governance verisi
 * @return array İşlenmiş DAO governance bilgileri
 */
function processDaoGovernanceData($jsonData)
{
    if (empty($jsonData)) {
        return [
            'governance_mechanisms' => [],
            'total_mechanisms' => 0,
            'governance_types' => [],
            'governance_score' => 'Unknown',
            'is_decentralized' => false,
            'has_token_voting' => false,
            'has_delegation' => false,
            'has_multisig' => false,
            'summary' => 'No DAO governance data available'
        ];
    }

    // JSON verisini decode et
    $data = json_decode($jsonData, true);

    if (!is_array($data) || empty($data)) {
        return [
            'governance_mechanisms' => [],
            'total_mechanisms' => 0,
            'governance_types' => [],
            'governance_score' => 'Unknown',
            'is_decentralized' => false,
            'has_token_voting' => false,
            'has_delegation' => false,
            'has_multisig' => false,
            'summary' => 'Invalid DAO governance data'
        ];
    }

    $processedMechanisms = [];
    $governanceTypes = [];
    $decentralizedCount = 0;
    $tokenVotingCount = 0;
    $delegationCount = 0;
    $multisigCount = 0;
    $limitedCount = 0;

    foreach ($data as $record) {
        if (isset($record['metric_subtype']) && isset($record['source_text'])) {
            $mechanism = [
                'type' => $record['metric_subtype'],
                'description' => $record['source_text'],
                'analysis' => $record['check_reason'] ?? '',
                'check_status' => $record['check'] ?? '0',
                'date' => $record['date'] ?? '',
                'is_positive' => $record['check'] === '1'
            ];

            $processedMechanisms[] = $mechanism;

            // Mechanism türlerini say ve kategorize et
            $type = strtolower($record['metric_subtype']);
            $description = strtolower($record['source_text']);
            $analysis = strtolower($record['check_reason'] ?? '');

            if (!isset($governanceTypes[$record['metric_subtype']])) {
                $governanceTypes[$record['metric_subtype']] = 0;
            }
            $governanceTypes[$record['metric_subtype']]++;

            // Decentralized governance kontrolü
            if (stripos($type, 'decentralized') !== false ||
                stripos($description, 'decentralized') !== false ||
                stripos($analysis, 'decentralized') !== false) {
                $decentralizedCount++;
            }

            // Token voting kontrolü
            if (stripos($type, 'token') !== false ||
                stripos($description, 'token') !== false ||
                stripos($description, 'voting') !== false ||
                stripos($analysis, 'token holders') !== false ||
                stripos($analysis, 'voting') !== false) {
                $tokenVotingCount++;
            }

            // Delegation kontrolü
            if (stripos($description, 'delegate') !== false ||
                stripos($analysis, 'delegate') !== false) {
                $delegationCount++;
            }

            // Multisig kontrolü
            if (stripos($type, 'multisig') !== false ||
                stripos($description, 'multisig') !== false ||
                stripos($analysis, 'multisig') !== false) {
                $multisigCount++;
            }

            // Limited governance kontrolü
            if (stripos($type, 'limited') !== false ||
                stripos($analysis, 'limited') !== false ||
                stripos($analysis, 'strictly limited') !== false) {
                $limitedCount++;
            }
        }
    }

    // Governance features belirle
    $isDecentralized = $decentralizedCount > 0 && $limitedCount == 0;
    $hasTokenVoting = $tokenVotingCount > 0;
    $hasDelegation = $delegationCount > 0;
    $hasMultisig = $multisigCount > 0;

    // Governance score belirle
    $governanceScore = 'None';
    $totalMechanisms = count($processedMechanisms);

    // Score hesaplama: pozitif mekanizmalar ve özellikler
    $positiveCount = 0;
    foreach ($processedMechanisms as $mechanism) {
        if ($mechanism['is_positive']) {
            $positiveCount++;
        }
    }

    $score = $positiveCount;
    if ($isDecentralized) $score += 2;  // Decentralization is very important
    if ($hasTokenVoting) $score += 1;   // Token voting adds community power
    if ($hasDelegation) $score += 1;    // Delegation improves participation
    if ($limitedCount > 0) $score -= 2; // Limited governance reduces score
    if ($hasMultisig && !$isDecentralized) $score -= 1; // Multisig without decentralization is centralized

    if ($score >= 5) {
        $governanceScore = 'Excellent';
    } elseif ($score >= 4) {
        $governanceScore = 'High';
    } elseif ($score >= 2) {
        $governanceScore = 'Medium';
    } elseif ($score >= 1) {
        $governanceScore = 'Low';
    } else {
        $governanceScore = 'Poor';
    }

    // Özet bilgi oluştur
    $summary = '';
    if (!empty($governanceTypes)) {
        $uniqueTypes = count($governanceTypes);
        $summary = "{$totalMechanisms} governance mechanism(s) across {$uniqueTypes} type(s)";

        $features = [];
        if ($isDecentralized) {
            $features[] = "decentralized";
        }
        if ($hasTokenVoting) {
            $features[] = "token-based voting";
        }
        if ($hasDelegation) {
            $features[] = "delegation support";
        }
        if ($hasMultisig) {
            $features[] = "multisig governance";
        }
        if ($limitedCount > 0) {
            $features[] = "limited powers";
        }

        if (!empty($features)) {
            $summary .= " with " . implode(', ', $features);
        }

        $summary .= ". {$positiveCount} positive governance implementation(s)";
    }

    return [
        'governance_mechanisms' => $processedMechanisms,
        'total_mechanisms' => count($processedMechanisms),
        'governance_types' => $governanceTypes,
        'positive_mechanisms' => $positiveCount,
        'decentralized_count' => $decentralizedCount,
        'token_voting_count' => $tokenVotingCount,
        'delegation_count' => $delegationCount,
        'multisig_count' => $multisigCount,
        'limited_count' => $limitedCount,
        'governance_score' => $governanceScore,
        'is_decentralized' => $isDecentralized,
        'has_token_voting' => $hasTokenVoting,
        'has_delegation' => $hasDelegation,
        'has_multisig' => $hasMultisig,
        'summary' => $summary
    ];
}

/**
 * Metric ID 42 için revenue_sharing JSON verisini işler ve anlamlı bir obje döndürür
 *
 * @param string $jsonData JSON formatında revenue sharing verisi
 * @return array İşlenmiş revenue sharing bilgileri
 */
function processRevenueSharingData($jsonData)
{
    if (empty($jsonData)) {
        return [
            'revenue_sharing_mechanisms' => [],
            'total_mechanisms' => 0,
            'sharing_types' => [],
            'revenue_sharing_score' => 'Unknown',
            'has_profit_sharing' => false,
            'has_fee_distribution' => false,
            'has_passive_income' => false,
            'requires_staking' => false,
            'summary' => 'No revenue sharing data available'
        ];
    }

    // JSON verisini decode et
    $data = json_decode($jsonData, true);

    if (!is_array($data) || empty($data)) {
        return [
            'revenue_sharing_mechanisms' => [],
            'total_mechanisms' => 0,
            'sharing_types' => [],
            'revenue_sharing_score' => 'Unknown',
            'has_profit_sharing' => false,
            'has_fee_distribution' => false,
            'has_passive_income' => false,
            'requires_staking' => false,
            'summary' => 'Invalid revenue sharing data'
        ];
    }

    $processedMechanisms = [];
    $sharingTypes = [];
    $profitSharingCount = 0;
    $feeDistributionCount = 0;
    $passiveIncomeCount = 0;
    $stakingRequiredCount = 0;
    $positiveCount = 0;

    foreach ($data as $record) {
        if (isset($record['metric_subtype']) && isset($record['source_text'])) {
            $mechanism = [
                'type' => $record['metric_subtype'],
                'description' => $record['source_text'],
                'analysis' => $record['check_reason'] ?? '',
                'check_status' => $record['check'] ?? '0',
                'date' => $record['date'] ?? '',
                'is_positive' => $record['check'] === '1'
            ];

            $processedMechanisms[] = $mechanism;

            // Mechanism türlerini say ve kategorize et
            $type = strtolower($record['metric_subtype']);
            $description = strtolower($record['source_text']);
            $analysis = strtolower($record['check_reason'] ?? '');

            if (!isset($sharingTypes[$record['metric_subtype']])) {
                $sharingTypes[$record['metric_subtype']] = 0;
            }
            $sharingTypes[$record['metric_subtype']]++;

            // Pozitif değerlendirme kontrolü
            if ($record['check'] === '1') {
                $positiveCount++;
            }

            // Profit sharing kontrolü
            if (stripos($type, 'profit') !== false ||
                stripos($description, 'profit') !== false ||
                stripos($analysis, 'profit') !== false) {
                $profitSharingCount++;
            }

            // Fee distribution kontrolü
            if (stripos($type, 'fee') !== false ||
                stripos($description, 'fee') !== false ||
                stripos($analysis, 'fee') !== false ||
                stripos($description, 'gas') !== false) {
                $feeDistributionCount++;
            }

            // Passive income kontrolü (staking veya holding ile gelir)
            if (stripos($description, 'staking') !== false ||
                stripos($description, 'stake') !== false ||
                stripos($description, 'holding') !== false ||
                stripos($analysis, 'passive') !== false ||
                stripos($analysis, 'staking') !== false) {
                $passiveIncomeCount++;
            }

            // Staking requirement kontrolü
            if (stripos($description, 'staking') !== false ||
                stripos($description, 'stake') !== false ||
                stripos($description, 'stakers') !== false ||
                stripos($analysis, 'staking') !== false ||
                stripos($analysis, 'stakers') !== false) {
                $stakingRequiredCount++;
            }
        }
    }

    // Revenue sharing features belirle
    $hasProfitSharing = $profitSharingCount > 0;
    $hasFeeDistribution = $feeDistributionCount > 0;
    $hasPassiveIncome = $passiveIncomeCount > 0;
    $requiresStaking = $stakingRequiredCount > 0;

    // Revenue sharing score belirle
    $revenueSharingScore = 'None';
    $totalMechanisms = count($processedMechanisms);

    // Score hesaplama: pozitif mekanizmalar ve özellikler
    $score = $positiveCount;
    if ($hasProfitSharing) $score += 2;      // Profit sharing is the strongest form
    if ($hasFeeDistribution) $score += 1;    // Fee distribution is good
    if ($hasPassiveIncome) $score += 1;      // Passive income is attractive
    if ($requiresStaking && $positiveCount > 0) $score += 1; // Staking requirement with positive mechanisms

    if ($score >= 6) {
        $revenueSharingScore = 'Excellent';
    } elseif ($score >= 4) {
        $revenueSharingScore = 'High';
    } elseif ($score >= 2) {
        $revenueSharingScore = 'Medium';
    } elseif ($score >= 1) {
        $revenueSharingScore = 'Low';
    } else {
        $revenueSharingScore = 'Poor';
    }

    // Özet bilgi oluştur
    $summary = '';
    if (!empty($sharingTypes)) {
        $uniqueTypes = count($sharingTypes);
        $summary = "{$totalMechanisms} revenue sharing mechanism(s) across {$uniqueTypes} type(s)";

        $features = [];
        if ($hasProfitSharing) {
            $features[] = "profit sharing";
        }
        if ($hasFeeDistribution) {
            $features[] = "fee distribution";
        }
        if ($hasPassiveIncome) {
            $features[] = "passive income";
        }
        if ($requiresStaking) {
            $features[] = "staking required";
        }

        if (!empty($features)) {
            $summary .= " with " . implode(', ', $features);
        }

        $summary .= ". {$positiveCount} positive revenue sharing implementation(s)";

        if ($profitSharingCount > 0) {
            $summary .= ". {$profitSharingCount} profit sharing mechanism(s)";
        }
        if ($feeDistributionCount > 0) {
            $summary .= ". {$feeDistributionCount} fee distribution mechanism(s)";
        }
    }

    return [
        'revenue_sharing_mechanisms' => $processedMechanisms,
        'total_mechanisms' => count($processedMechanisms),
        'sharing_types' => $sharingTypes,
        'positive_mechanisms' => $positiveCount,
        'profit_sharing_count' => $profitSharingCount,
        'fee_distribution_count' => $feeDistributionCount,
        'passive_income_count' => $passiveIncomeCount,
        'staking_required_count' => $stakingRequiredCount,
        'revenue_sharing_score' => $revenueSharingScore,
        'has_profit_sharing' => $hasProfitSharing,
        'has_fee_distribution' => $hasFeeDistribution,
        'has_passive_income' => $hasPassiveIncome,
        'requires_staking' => $requiresStaking,
        'summary' => $summary
    ];
}

?>
