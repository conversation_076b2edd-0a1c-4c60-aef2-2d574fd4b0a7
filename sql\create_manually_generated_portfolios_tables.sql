-- Manually Generated Portfolios Tables
-- Bu tablolar kullanıcıların AI tarafından oluşturulan manuel portföy templatelerini saklar

-- Ana portföy tablosu
CREATE TABLE IF NOT EXISTS manually_generated_portfolios (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    template_id VARCHAR(100) NOT NULL, -- AI tarafından oluşturulan unique ID (örn: ai-generated-medium-1735689600)
    name VARCHAR(255) NOT NULL,
    description TEXT,
    risk_level ENUM('low', 'medium', 'high') NOT NULL,
    timeframe VARCHAR(50) NULL, -- short, medium, long veya custom değerler
    template_name VARCHAR(255) NULL, -- AI'ın verdiği template adı
    total_score DECIMAL(5,2) NULL, -- AI'ın hesapladığı toplam skor
    strategy TEXT NULL, -- Portföy stratejisi açıklaması
    reasoning_explanation TEXT NULL, -- AI'ın portföy seçim gerek<PERSON>esi
    user_context TEXT NULL, -- Kullanıcı profili ve bağlam bilgisi
    metrics JSON NULL, -- AI'ın verdiği ek metrikler
    
    -- Kullanıcı input parametreleri (gelecekte analiz için)
    investment_budget DECIMAL(15,2) NULL,
    budget_category VARCHAR(100) NULL,
    risk_profile VARCHAR(100) NULL,
    investment_timeframe VARCHAR(100) NULL,
    selected_categories JSON NULL, -- Seçilen kategoriler array
    ai_prompt TEXT NULL, -- AI'a gönderilen tam prompt
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_user_id (user_id),
    INDEX idx_template_id (template_id),
    INDEX idx_risk_level (risk_level),
    INDEX idx_created_at (created_at),
    INDEX idx_user_created (user_id, created_at),
    
    -- Foreign key constraint
    CONSTRAINT fk_manually_generated_portfolios_user_id 
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Portföy varlıkları tablosu
CREATE TABLE IF NOT EXISTS manually_generated_portfolio_assets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    portfolio_id INT NOT NULL,
    coin_id INT NOT NULL, -- coindata tablosundaki coin ID
    symbol VARCHAR(20) NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    percentage DECIMAL(5,2) NOT NULL, -- Allocation yüzdesi (0.00 to 100.00)
    score DECIMAL(5,2) NULL, -- Coin'in AI skoru
    description TEXT NULL, -- AI'ın coin seçim açıklaması
    category VARCHAR(100) NULL, -- Coin kategorisi
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_portfolio_id (portfolio_id),
    INDEX idx_coin_id (coin_id),
    INDEX idx_symbol (symbol),
    UNIQUE KEY uniq_portfolio_coin (portfolio_id, coin_id),
    
    -- Foreign key constraints
    CONSTRAINT fk_manually_generated_portfolio_assets_portfolio_id 
    FOREIGN KEY (portfolio_id) REFERENCES manually_generated_portfolios(id) ON DELETE CASCADE,
    
    CONSTRAINT fk_manually_generated_portfolio_assets_coin_id 
    FOREIGN KEY (coin_id) REFERENCES coindata(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tablo açıklamaları:
-- manually_generated_portfolios:
-- - user_id: Portföyü oluşturan kullanıcının ID'si
-- - template_id: AI tarafından oluşturulan benzersiz template ID
-- - name: Portföy adı (örn: "AI Medium Risk Portföyü")
-- - description: Portföy açıklaması
-- - risk_level: Risk seviyesi (low, medium, high)
-- - timeframe: Yatırım vadesi
-- - template_name: AI'ın verdiği template adı
-- - total_score: AI'ın hesapladığı toplam skor
-- - strategy: Portföy stratejisi
-- - reasoning_explanation: AI'ın gerekçe açıklaması
-- - user_context: Kullanıcı profili bilgisi
-- - metrics: AI'ın verdiği ek metrikler (JSON)
-- - investment_budget: Kullanıcının belirttiği bütçe
-- - budget_category: Bütçe kategorisi
-- - risk_profile: Kullanıcının risk profili
-- - investment_timeframe: Kullanıcının yatırım vadesi
-- - selected_categories: Seçilen coin kategorileri
-- - ai_prompt: AI'a gönderilen tam prompt metni

-- manually_generated_portfolio_assets:
-- - portfolio_id: Bağlı olduğu portföy ID'si
-- - coin_id: Coin'in veritabanındaki ID'si
-- - symbol: Coin sembolü (BTC, ETH, vb.)
-- - full_name: Coin'in tam adı
-- - percentage: Portföydeki allocation yüzdesi
-- - score: Coin'in AI skoru
-- - description: AI'ın bu coin için açıklaması
-- - category: Coin'in kategorisi
