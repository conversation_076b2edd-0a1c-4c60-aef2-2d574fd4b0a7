-- Predefined Portfolio Templates Tables
-- Minimal structure for predefined templates that users can select and create portfolios from

CREATE TABLE IF NOT EXISTS predefined_portfolio_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON>HAR(255) NOT NULL,
    description TEXT,
    risk_level ENUM('low', 'medium', 'high') NOT NULL,
    timeframe ENUM('short', 'medium', 'long') NOT NULL,
    strategy TEXT,
    suitable_for JSON,
    not_suitable_for JSON,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_risk_level (risk_level),
    INDEX idx_timeframe (timeframe),
    INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS predefined_template_assets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    template_id INT NOT NULL,
    coin_id INT NOT NULL,
    percentage DECIMAL(5,2) NOT NULL, -- Allocation percentage (0.00 to 100.00)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_template_id (template_id),
    INDEX idx_coin_id (coin_id),
    UNIQUE KEY uniq_template_coin (template_id, coin_id),
    CONSTRAINT fk_predefined_template FOREIGN KEY (template_id) REFERENCES predefined_portfolio_templates(id) ON DELETE CASCADE,
    CONSTRAINT fk_predefined_coin FOREIGN KEY (coin_id) REFERENCES coindata(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert sample predefined templates
INSERT INTO predefined_portfolio_templates (name, description, risk_level, timeframe, strategy, suitable_for, not_suitable_for) VALUES
('Conservative Crypto', 'Düşük riskli, büyük piyasa değerli coinlerden oluşan güvenli portföy', 'low', 'long', 'Büyük piyasa değerli ve stabil coinlere odaklanarak risk minimizasyonu', '["Yeni başlayanlar", "Risk almak istemeyen yatırımcılar", "Uzun vadeli yatırımcılar"]', '["Yüksek getiri arayan yatırımcılar", "Kısa vadeli spekülatörler"]'),
('Balanced Growth', 'Orta risk seviyesinde dengeli büyüme odaklı portföy', 'medium', 'medium', 'Büyük ve orta ölçekli projelerin karışımı ile dengeli büyüme', '["Deneyimli yatırımcılar", "Orta vadeli hedefleri olan yatırımcılar"]', '["Çok muhafazakar yatırımcılar", "Yüksek risk toleransı olan yatırımcılar"]'),
('High Growth Potential', 'Yüksek büyüme potansiyeli olan altcoinlere odaklanan agresif portföy', 'high', 'short', 'Yüksek potansiyelli altcoinler ve yeni projelerle maksimum büyüme hedefi', '["Deneyimli kripto yatırımcıları", "Yüksek risk toleransı olan yatırımcılar", "Kısa-orta vadeli yatırımcılar"]', '["Yeni başlayanlar", "Risk almak istemeyen yatırımcılar", "Emeklilik fonları"]);