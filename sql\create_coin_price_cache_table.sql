-- Coin price cache table (15-minute TTL used by queries)
-- Run this SQL to create the cache table

CREATE TABLE IF NOT EXISTS coin_price_cache (
    coin_id INT NOT NULL,
    price DECIMAL(36,18) NOT NULL,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (coin_id),
    INDEX idx_updated_at (updated_at)
);

-- Optional: add a foreign key if coindata.id is guaranteed to exist and you want cascading cleanup
-- ALTER TABLE coin_price_cache
--   ADD CONSTRAINT fk_coin_price_cache_coin
--   FOREIGN KEY (coin_id) REFERENCES coindata(id) ON DELETE CASCADE;

