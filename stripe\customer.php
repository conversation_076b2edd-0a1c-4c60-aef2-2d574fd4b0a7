<?php
/**
 * Stripe Customer Functions
 *
 * This file contains functions related to Stripe customers.
 */

// Include required files
require_once __DIR__ . '/StripeLogger.php';

/**
 * Handle new customer creation
 *
 * @param object $customer The customer object from Stripe
 */
function handleNewCustomer($customer) {
    global $link;

    $email = $customer->email;
    $stripeCustomerId = $customer->id;

    StripeLogger::log(StripeLogLevel::INFO, "STRIPE NEW CUSTOMER - Email: $email, Customer ID: $stripeCustomerId");

    // Check if user already exists
    $query = "SELECT id, email FROM users WHERE email = ?";
    $stmt = mysqli_prepare($link, $query);

    if ($stmt) {
        mysqli_stmt_bind_param($stmt, "s", $email);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $user = mysqli_fetch_assoc($result);

        if ($user) {
            // User exists, update Stripe customer ID only
            // We don't update the email address as users might use different emails for Stripe
            $updateQuery = "UPDATE users SET stripe_customer_id = ? WHERE id = ?";
            $updateStmt = mysqli_prepare($link, $updateQuery);
            mysqli_stmt_bind_param($updateStmt, "si", $stripeCustomerId, $user['id']);
            mysqli_stmt_execute($updateStmt);
            StripeLogger::log(StripeLogLevel::INFO, "STRIPE CUSTOMER ID UPDATED FOR EXISTING USER - User ID: {$user['id']}, Email: {$user['email']}");
            return;
        }

        // User doesn't exist, create new user
        mysqli_begin_transaction($link);

        try {
            // Generate a username from email
            $username = explode('@', $email)[0];
            $registeredVia = 'stripe';
            $created_at = date('Y-m-d H:i:s');
            $subscriptionLevel = 'free'; // Default to free

            // Insert new user - Stripe users have verified emails by default
            $emailVerified = 1; // Stripe already verifies emails
            $insertQuery = "INSERT INTO users (email, username, registered_via, stripe_customer_id, subscription_level, email_verified, created_at)
                           VALUES (?, ?, ?, ?, ?, ?, ?)";
            $insertStmt = mysqli_prepare($link, $insertQuery);
            mysqli_stmt_bind_param($insertStmt, "sssssss", $email, $username, $registeredVia, $stripeCustomerId, $subscriptionLevel, $emailVerified, $created_at);

            if (!mysqli_stmt_execute($insertStmt)) {
                throw new Exception('User registration failed.');
            }

            $userId = mysqli_insert_id($link);

            // Create default watchlist
            $watchlistName = 'My Watchlist';
            $insertWatchlistQuery = "INSERT INTO watchlists (user_id, name, created_at) VALUES (?, ?, ?)";
            $insertWatchlistStmt = mysqli_prepare($link, $insertWatchlistQuery);
            mysqli_stmt_bind_param($insertWatchlistStmt, "iss", $userId, $watchlistName, $created_at);

            if (!mysqli_stmt_execute($insertWatchlistStmt)) {
                throw new Exception('Watchlist creation failed.');
            }

            // Create default portfolio
            $portfolioName = 'My Portfolio';
            $insertPortfolioQuery = "INSERT INTO portfolio (user_id, name, created_at) VALUES (?, ?, ?)";
            $insertPortfolioStmt = mysqli_prepare($link, $insertPortfolioQuery);
            mysqli_stmt_bind_param($insertPortfolioStmt, "iss", $userId, $portfolioName, $created_at);

            if (!mysqli_stmt_execute($insertPortfolioStmt)) {
                throw new Exception('Portfolio creation failed.');
            }

            mysqli_commit($link);
            StripeLogger::log(StripeLogLevel::INFO, "STRIPE NEW USER CREATED - Email: $email, User ID: $userId, Customer ID: $stripeCustomerId");

        } catch (Exception $e) {
            mysqli_rollback($link);
            StripeLogger::log(StripeLogLevel::ERROR, "STRIPE USER CREATION ERROR: " . $e->getMessage(), [
                'exception' => get_class($e),
                'email' => $email ?? 'unknown',
                'stripeCustomerId' => $stripeCustomerId ?? 'unknown'
            ]);
        }
    }
}

/**
 * Handle customer update
 *
 * @param object $customer The customer object from Stripe
 */
function handleUpdatedCustomer($customer) {
    global $link;

    $email = $customer->email;
    $stripeCustomerId = $customer->id;

    StripeLogger::log(StripeLogLevel::INFO, "STRIPE CUSTOMER UPDATED - Email: $email, Customer ID: $stripeCustomerId");

    // Check if user exists with this stripe_customer_id
    $query = "SELECT id FROM users WHERE stripe_customer_id = ?";
    $stmt = mysqli_prepare($link, $query);

    if ($stmt) {
        mysqli_stmt_bind_param($stmt, "s", $stripeCustomerId);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $user = mysqli_fetch_assoc($result);

        if ($user) {
            // User exists, but we don't update the email address
            // Users might use different email addresses for Stripe and our site
            StripeLogger::log(StripeLogLevel::DEBUG, "STRIPE CUSTOMER FOUND - User ID: {$user['id']}, Customer ID: $stripeCustomerId");
        } else {
            // Try to find user by email
            $query = "SELECT id FROM users WHERE email = ?";
            $stmt = mysqli_prepare($link, $query);
            mysqli_stmt_bind_param($stmt, "s", $email);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $user = mysqli_fetch_assoc($result);

            if ($user) {
                // User found by email, update stripe_customer_id
                $updateQuery = "UPDATE users SET stripe_customer_id = ? WHERE id = ?";
                $updateStmt = mysqli_prepare($link, $updateQuery);
                mysqli_stmt_bind_param($updateStmt, "si", $stripeCustomerId, $user['id']);
                mysqli_stmt_execute($updateStmt);
                StripeLogger::log(StripeLogLevel::INFO, "User updated with Stripe customer ID: $email (ID: {$user['id']})");
            } else {
                // No user found, create new user
                handleNewCustomer($customer);
            }
        }
    }
}
