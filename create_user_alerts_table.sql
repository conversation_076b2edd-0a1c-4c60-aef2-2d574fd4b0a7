-- <PERSON><PERSON> script to create the user_alerts table

CREATE TABLE IF NOT EXISTS user_alerts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    coin_id VARCHAR(255) NOT NULL,
    alert_type ENUM('ai-score', 'price') NOT NULL,
    notification_type <PERSON>NU<PERSON>('browser', 'email', 'push') NOT NULL,
    condition_above BOOLEAN DEFAULT FALSE,
    condition_below BOOLEAN DEFAULT FALSE,
    threshold_above DECIMAL(10, 2) NULL,
    threshold_below DECIMAL(10, 2) NULL,
    price_above DECIMAL(20, 8) NULL,
    price_below DECIMAL(20, 8) NULL,
    is_triggered_above BOOLEAN DEFAULT FALSE,
    is_triggered_below BOOLEAN DEFAULT FALSE,
    triggered_above_at TIMESTAMP NULL,
    triggered_below_at TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX (user_id),
    INDEX (coin_id),
    INDEX (alert_type)
);

-- Add comments to explain the table structure
-- user_id: The ID of the user who created the alert
-- coin_id: The ID of the coin for which the alert is created
-- alert_type: The type of alert (ai-score, price)
-- notification_type: The type of notification (browser, email, push)
-- condition_above: Whether to trigger when value goes above threshold
-- condition_below: Whether to trigger when value goes below threshold
-- threshold_above: The threshold value for above condition (for ai-score)
-- threshold_below: The threshold value for below condition (for ai-score)
-- price_above: The price threshold for above condition (for price)
-- price_below: The price threshold for below condition (for price)
-- is_triggered_above: Whether the above condition has been triggered
-- is_triggered_below: Whether the below condition has been triggered
-- triggered_above_at: When the above condition was triggered
-- triggered_below_at: When the below condition was triggered
-- is_active: Whether the alert is active
-- created_at: When the alert was created
-- updated_at: When the alert was last updated

-- Migration script to update existing table
-- Run this if you already have the user_alerts table:
/*
ALTER TABLE user_alerts
ADD COLUMN is_triggered_above BOOLEAN DEFAULT FALSE AFTER price_below,
ADD COLUMN is_triggered_below BOOLEAN DEFAULT FALSE AFTER is_triggered_above,
ADD COLUMN triggered_above_at TIMESTAMP NULL AFTER is_triggered_below,
ADD COLUMN triggered_below_at TIMESTAMP NULL AFTER triggered_above_at;

-- Drop old columns if they exist
ALTER TABLE user_alerts
DROP COLUMN IF EXISTS is_triggered,
DROP COLUMN IF EXISTS triggered_at;
*/
