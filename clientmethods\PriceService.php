<?php
include_once(__DIR__ . '/../config.php');
require_once(__DIR__ . '/../stripe/StripeLogger.php');
/**
 * PriceService
 * Centralized price fetching utilities. Use get_current_price_by_coin_id everywhere.
 * - Accepts either numeric coin ID (our internal coindata.id) or geckoslug string
 * - Prefers CryptoRank data, falls back to CoinGecko on failure
 * - Added caching layer by coin_id
 */
function get_current_price_by_coin_id($value, $options = [])
{
    global $link;
    // Determine input type: geckoslug string or numeric internal coin id
    $isGeckoSlug = $options['isGeckoSlug'] ?? (!is_numeric($value));
    // Get coin info (id, cr_id, geckoslug)
    $stmt = mysqli_prepare($link, "SELECT id, cr_id, geckoslug FROM coindata WHERE " .
        ($isGeckoSlug ? "geckoslug = ?" : "id = ?") . " LIMIT 1");
    mysqli_stmt_bind_param($stmt, 's', $value);
    mysqli_stmt_execute($stmt);
    $rs = mysqli_stmt_get_result($stmt);
    $coin_id = null;
    $cr_id = null;
    $geckoslug = null;
    if ($rs && mysqli_num_rows($rs) > 0) {
        $row = mysqli_fetch_assoc($rs);
        $coin_id = $row['id'];
        $cr_id = $row['cr_id'];
        $geckoslug = $row['geckoslug'];
    }
    mysqli_stmt_close($stmt);
    if (empty($coin_id)) {
        return 0.0; // not found in DB
    }
    // --- Step 1: Try cache
    $cachedPrice = cache_get_price($coin_id);
    if ($cachedPrice !== null) {
        return (float)$cachedPrice;
    }
    // --- Step 2: Try CryptoRank
    if (!empty($cr_id)) {
        $price = fetch_price_from_cryptorank($cr_id);
        if ($price > 0) {
            cache_set_price($coin_id, $price); // save to cache
            return (float)$price;
        }
    }

    // // --- Step 3: Fallback to CoinGecko
    // if (!empty($geckoslug)) {
    //     $price = fetch_price_from_coingecko($geckoslug);
    //     if ($price > 0) {
    //         cache_set_price($coin_id, $price); // save to cache
    //         return (float)$price;
    //     }
    // }
    return 0.0;
}
/**
 * Dummy cache getter
 * For now always returns null (meaning "no cache available").
 */
function cache_get_price($coin_id)
{
    global $link;
    $sql = "SELECT price FROM coin_price_cache WHERE coin_id = ? AND updated_at >= (NOW() - INTERVAL 15 MINUTE) LIMIT 1";
    $stmt = mysqli_prepare($link, $sql);
    if ($stmt === false) {
        StripeLogger::log(StripeLogLevel::ERROR, 'cache_get_price prepare failed', [
            'coin_id' => $coin_id,
            'error' => mysqli_error($link),
        ]);
        return null;
    }
    mysqli_stmt_bind_param($stmt, 'i', $coin_id);
    if (!mysqli_stmt_execute($stmt)) {
        StripeLogger::log(StripeLogLevel::ERROR, 'cache_get_price execute failed', [
            'coin_id' => $coin_id,
            'error' => mysqli_stmt_error($stmt),
        ]);
        mysqli_stmt_close($stmt);
        return null;
    }
    $result = mysqli_stmt_get_result($stmt);
    $price = null;
    if ($result && mysqli_num_rows($result) > 0) {
        $row = mysqli_fetch_assoc($result);
        $price = (float)$row['price'];
    }
    mysqli_stmt_close($stmt);
    return $price;
}
/**
 * Dummy cache setter
 * For now always returns true as if the value is cached successfully.
 */
function cache_set_price($coin_id, $price)
{
    global $link;
    // Upsert into cache table; keep latest price and timestamp
    $sql = "INSERT INTO coin_price_cache (coin_id, price, updated_at) VALUES (?, ?, NOW())\n            ON DUPLICATE KEY UPDATE price = VALUES(price), updated_at = NOW()";
    $stmt = mysqli_prepare($link, $sql);
    if ($stmt === false) {
        StripeLogger::log(StripeLogLevel::ERROR, 'cache_set_price prepare failed', [
            'coin_id' => $coin_id,
            'price' => $price,
            'error' => mysqli_error($link),
        ]);
        return false;
    }
    // bind: i = int, d = double
    $priceFloat = (float)$price;
    mysqli_stmt_bind_param($stmt, 'id', $coin_id, $priceFloat);
    $ok = mysqli_stmt_execute($stmt);
    if (!$ok) {
        StripeLogger::log(StripeLogLevel::ERROR, 'cache_set_price execute failed', [
            'coin_id' => $coin_id,
            'price' => $priceFloat,
            'error' => mysqli_stmt_error($stmt),
        ]);
    }
    mysqli_stmt_close($stmt);
    return $ok;
}
function fetch_price_from_coingecko($geckoslug)
{
    global $gecko_api_key;
    $curl = curl_init();
    $url = 'https://pro-api.coingecko.com/api/v3/simple/price?ids=' .
        urlencode($geckoslug) . '&vs_currencies=usd&x_cg_pro_api_key=' . $gecko_api_key;
    curl_setopt_array($curl, array(
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_HTTPHEADER => array('Accept: application/json'),
    ));
    $response = curl_exec($curl);
    $httpcode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    $err = curl_error($curl);
    curl_close($curl);
    if ($httpcode != 200 || $response === false) {
        StripeLogger::log(StripeLogLevel::ERROR, 'CoinGecko price fetch failed', [
            'geckoslug' => $geckoslug,
            'http_code' => $httpcode,
            'error' => $err,
            'response' => $response,
        ]);
        return 0.0;
    }
    $data = json_decode($response, true);
    if (json_last_error() !== JSON_ERROR_NONE || !isset($data[$geckoslug]['usd'])) {
        return 0.0;
    }
    return (float)$data[$geckoslug]['usd'];
}
function fetch_price_from_cryptorank($cr_id)
{
    global $cr_api_key;
    if (!is_numeric($cr_id)) {
        return 0.0;
    }
    $curl = curl_init();
    $url = 'https://api.cryptorank.io/v2/currencies/' . $cr_id . '/full-metadata';
    curl_setopt_array($curl, array(
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_HTTPHEADER => array(
            'Accept: application/json',
            'X-Api-Key: ' . $cr_api_key
        ),
    ));
    $response = curl_exec($curl);
    $httpcode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    $err = curl_error($curl);
    curl_close($curl);
    if ($httpcode != 200 || $response === false) {
        StripeLogger::log(StripeLogLevel::ERROR, 'CryptoRank price fetch failed', [
            'cr_id' => $cr_id,
            'http_code' => $httpcode,
            'error' => $err,
            'response' => $response,
        ]);
        return 0.0;
    }
    $cr_data = json_decode($response, true);
    if (json_last_error() !== JSON_ERROR_NONE || !isset($cr_data['data'])) {
        return 0.0;
    }
    return (float)($cr_data['data']['price'] ?? 0);
}
