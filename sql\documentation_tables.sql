-- Documentation system tables - Simple structure

-- Main documentation items table
CREATE TABLE documentation_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    slug VARCHAR(255) NOT NULL UNIQUE, -- URL-friendly identifier (e.g., "max-supply")
    parent_id INT NULL, -- NULL for root level, parent ID for child levels
    order_index INT NOT NULL DEFAULT 0, -- For ordering
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- Foreign key constraint
    FOREIGN KEY (parent_id) REFERENCES documentation_items(id) ON DELETE CASCADE,

    -- Indexes
    INDEX idx_parent_id (parent_id),
    INDEX idx_slug (slug),
    INDEX idx_order (order_index)
);

-- Multi-language content table
CREATE TABLE documentation_translations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    item_id INT NOT NULL,
    language_code VARCHAR(5) NOT NULL, -- 'tr', 'en', 'ar', 'id', 'it'
    title VARCHAR(500) NOT NULL,
    content LONGTEXT NULL, -- <PERSON>down content
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- Foreign key constraint
    FOREIGN KEY (item_id) REFERENCES documentation_items(id) ON DELETE CASCADE,

    -- Each item can have only one translation per language
    UNIQUE KEY unique_item_language (item_id, language_code),

    -- Indexes
    INDEX idx_item_language (item_id, language_code),
    INDEX idx_language (language_code)
);

-- Sample data for testing
INSERT INTO documentation_items (slug, parent_id, order_index) VALUES
-- Root level items
('scoring-system', NULL, 1),
('api-reference', NULL, 2),

-- Level 2 items under scoring-system
('altcoin-score', 1, 1),
('social-score', 1, 2),

-- Level 3 items under altcoin-score
('tokenomics', 3, 1),
('team-analysis', 3, 2),

-- Level 4 items under tokenomics
('max-supply', 5, 1),
('token-distribution', 5, 2);

-- Sample translations
INSERT INTO documentation_translations (item_id, language_code, title, content) VALUES
-- Scoring System (no content, just structure)
(1, 'en', 'Scoring System', NULL),
(1, 'tr', 'Puanlama Sistemi', NULL),

-- API Reference
(2, 'en', 'API Reference', '# API Reference\n\nOur comprehensive API allows you to access all CoinScout data programmatically. Use our RESTful endpoints to retrieve coin data, market information, and user-specific content.'),
(2, 'tr', 'API Referansı', '# API Referansı\n\nKapsamlı API\'miz tüm CoinScout verilerine programatik olarak erişmenizi sağlar. Coin verileri, pazar bilgileri ve kullanıcıya özel içerikleri almak için RESTful endpoint\'lerimizi kullanın.'),

-- Altcoin Score
(3, 'en', 'Altcoin Score', NULL),
(3, 'tr', 'Altcoin Puanı', NULL),

-- Social Score
(4, 'en', 'Social Score', NULL),
(4, 'tr', 'Sosyal Puan', NULL),

-- Tokenomics
(5, 'en', 'Tokenomics', NULL),
(5, 'tr', 'Tokenomik', NULL),

-- Team Analysis
(6, 'en', 'Team Analysis', NULL),
(6, 'tr', 'Takım Analizi', NULL),

-- Max Supply (with content)
(7, 'en', 'Max Supply', '# Max Supply\n\nThe maximum supply of a token is a crucial metric that determines the total number of tokens that will ever exist. This metric directly impacts the token\'s scarcity and potential value appreciation over time.'),
(7, 'tr', 'Maksimum Arz', '# Maksimum Arz\n\nBir tokenın maksimum arzı, o tokendan hiç var olacak toplam sayıyı belirleyen kritik bir metriktir. Bu metrik, tokenın kıtlığını ve zaman içindeki potansiyel değer artışını doğrudan etkiler.'),

-- Token Distribution (with content)
(8, 'en', 'Token Distribution', '# Token Distribution\n\nToken distribution refers to how tokens are allocated among different stakeholders including founders, investors, community, and reserves. A fair distribution is crucial for long-term project success.'),
(8, 'tr', 'Token Dağılımı', '# Token Dağılımı\n\nToken dağılımı, tokenların kurucular, yatırımcılar, topluluk ve rezervler arasında nasıl tahsis edildiğini ifade eder. Adil bir dağılım, uzun vadeli proje başarısı için kritiktir.');
