
<?php

function fetchCoinDataFromDB($coinId) {
    global $link;
    $stmt = mysqli_prepare($link, "
        SELECT c.symbol, c.name, c.marketcap, c.fdv, c.total_volume, c.total_score,
               c.geckoid, c.geckoslug, c2.price_change_1d, c2.price_change_7d,
               (SELECT COUNT(*) > 0 FROM client_coin_list WHERE id = ?) as is_vetted
        FROM coindata c
        LEFT JOIN coindata2 c2 ON c.geckoid = c2.geckoid
        WHERE c.id = ? LIMIT 1
    ");

    if (!$stmt) return null;

    mysqli_stmt_bind_param($stmt, 'ii', $coinId, $coinId);
    if (!mysqli_stmt_execute($stmt)) {
        mysqli_stmt_close($stmt);
        return null;
    }

    $result = mysqli_stmt_get_result($stmt);
    $row = $result ? mysqli_fetch_assoc($result) : null;
    mysqli_stmt_close($stmt);

    if (!$row) return null;

    // Kategorileri çek
    $categories = [];
    if (!empty($row['geckoid'])) {
        $stmtCat = mysqli_prepare($link, "
            SELECT cc.category_name, COALESCE(gc.market_cap, 0) as market_cap, gc.geckoid as gid
            FROM coindata_categories cc
            LEFT JOIN gecko_categories gc ON cc.category_name = gc.name
            WHERE cc.geckoid = ? AND COALESCE(gc.market_cap, 0) > 0
            ORDER BY COALESCE(gc.market_cap, 0) DESC LIMIT 5
        ");

        if ($stmtCat) {
            mysqli_stmt_bind_param($stmtCat, 's', $row['geckoid']);
            if (mysqli_stmt_execute($stmtCat)) {
                $resCat = mysqli_stmt_get_result($stmtCat);
                while ($catRow = mysqli_fetch_assoc($resCat)) {
                    $categories[] = [
                        'name' => $catRow['category_name'],
                        'marketCap' => (float)$catRow['market_cap'],
                        'gid' => $catRow['gid']
                    ];
                }
            }
            mysqli_stmt_close($stmtCat);
        }
    }

    // Fallback: Eğer geckoid ile kategori bulunamadıysa, geckoslug ile tekrar dene
    if (empty($categories) && !empty($row['geckoslug'])) {
        $stmtCat2 = mysqli_prepare($link, "
            SELECT cc.category_name, COALESCE(gc.market_cap, 0) as market_cap, gc.geckoid as gid
            FROM coindata_categories cc
            LEFT JOIN gecko_categories gc ON cc.category_name = gc.name
            WHERE cc.geckoid = ? AND COALESCE(gc.market_cap, 0) > 0
            ORDER BY COALESCE(gc.market_cap, 0) DESC LIMIT 5
        ");

        if ($stmtCat2) {
            $geckoslugStr = (string)$row['geckoslug'];
            mysqli_stmt_bind_param($stmtCat2, 's', $geckoslugStr);
            if (mysqli_stmt_execute($stmtCat2)) {
                $resCat2 = mysqli_stmt_get_result($stmtCat2);
                while ($catRow2 = mysqli_fetch_assoc($resCat2)) {
                    $categories[] = [
                        'name' => $catRow2['category_name'],
                        'marketCap' => (float)$catRow2['market_cap'],
                        'gid' => $catRow2['gid']
                    ];
                }
            }
            mysqli_stmt_close($stmtCat2);
        }
    }


    // Anlık fiyatı çek
    $currentPrice = null;
    if (!empty($row['geckoslug'])) {
        require_once __DIR__ . '/clientmethods/PriceService.php';
        $currentPrice = get_current_price_by_coin_id($row['geckoslug'], ['isGeckoSlug' => true]);
    }

    return [
        'symbol' => $row['symbol'],
        'name' => $row['name'],
        'marketcap' => (float)$row['marketcap'],
        'fdv' => (float)$row['fdv'],
        'total_volume' => (float)$row['total_volume'],
        'total_score' => (float)$row['total_score'],
        'is_vetted' => (bool)$row['is_vetted'],
        'price_change_1d' => (float)$row['price_change_1d'],
        'price_change_7d' => (float)$row['price_change_7d'],
        'current_price' => $currentPrice,
        'categories' => $categories
    ];
}
?>


