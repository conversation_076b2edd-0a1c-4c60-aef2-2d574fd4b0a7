<?php
/**
 * Stripe Invoice Functions
 *
 * This file contains functions related to Stripe invoices.
 */

// Include required files
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../secrets.php';
require_once __DIR__ . '/../utils.php';
require_once __DIR__ . '/../models/ResultModel.php';
require_once __DIR__ . '/StripeLogger.php';

// Set Stripe API key
\Stripe\Stripe::setApiKey($stripeSecretKey);

/**
 * Handle successful invoice event
 *
 * @param object $invoice The invoice object from Stripe
 */
function handleSuccessfulInvoice($invoice) {
    global $link;

    // Process successful invoice
    $invoiceId = $invoice->id;
    $amount = $invoice->amount_paid;
    $currency = $invoice->currency;
    $customer = $invoice->customer;
    $subscriptionId = $invoice->subscription;
    $created = date('Y-m-d H:i:s', $invoice->created);
    $periodStart = date('Y-m-d H:i:s', $invoice->period_start);
    $periodEnd = date('Y-m-d H:i:s', $invoice->period_end);

    StripeLogger::log(StripeLogLevel::INFO, "STRIPE INVOICE PAID - Amount: $amount $currency - Customer: $customer, Invoice ID: $invoiceId");

    try {
        // Find user by stripe_customer_id
        $userId = null;

        // 1. First try to find user by stripe_customer_id
        $query = "SELECT id FROM users WHERE stripe_customer_id = ?";
        $stmt = mysqli_prepare($link, $query);

        if (!$stmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($stmt, "s", $customer);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $user = mysqli_fetch_assoc($result);

        if ($user) {
            $userId = $user['id'];
            StripeLogger::log(StripeLogLevel::DEBUG, "STRIPE USER FOUND BY CUSTOMER ID - User ID: $userId");
        }

        // 2. If user not found and subscription exists, try to find by subscription
        if (!$userId && $subscriptionId) {
            $query = "SELECT user_id FROM stripe_user_subscriptions WHERE stripe_subscription_id = ?";
            $stmt = mysqli_prepare($link, $query);

            if (!$stmt) {
                throw new Exception("Database error: " . mysqli_error($link));
            }

            mysqli_stmt_bind_param($stmt, "s", $subscriptionId);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $subscription = mysqli_fetch_assoc($result);

            if ($subscription) {
                $userId = $subscription['user_id'];
                StripeLogger::log(StripeLogLevel::DEBUG, "STRIPE USER FOUND BY SUBSCRIPTION ID - User ID: $userId");

                // Update user's stripe_customer_id if it's empty
                $updateQuery = "UPDATE users SET stripe_customer_id = ? WHERE id = ? AND (stripe_customer_id IS NULL OR stripe_customer_id = '')";
                $updateStmt = mysqli_prepare($link, $updateQuery);

                if ($updateStmt) {
                    mysqli_stmt_bind_param($updateStmt, "si", $customer, $userId);
                    mysqli_stmt_execute($updateStmt);
                    StripeLogger::log(StripeLogLevel::INFO, "STRIPE CUSTOMER ID UPDATED - User ID: $userId, Customer ID: $customer");
                }
            }
        }

        // 3. If still no user found, check checkout sessions
        if (!$userId && $subscriptionId) {
            $query = "SELECT client_reference_id FROM stripe_checkout_sessions WHERE subscription_id = ?";
            $stmt = mysqli_prepare($link, $query);

            if (!$stmt) {
                throw new Exception("Database error: " . mysqli_error($link));
            }

            mysqli_stmt_bind_param($stmt, "s", $subscriptionId);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $session = mysqli_fetch_assoc($result);

            if ($session && $session['client_reference_id']) {
                $userId = $session['client_reference_id'];
                StripeLogger::log(StripeLogLevel::DEBUG, "STRIPE USER FOUND BY CHECKOUT SESSION - User ID: $userId");

                // Update user's stripe_customer_id if it's empty
                $updateQuery = "UPDATE users SET stripe_customer_id = ? WHERE id = ? AND (stripe_customer_id IS NULL OR stripe_customer_id = '')";
                $updateStmt = mysqli_prepare($link, $updateQuery);

                if ($updateStmt) {
                    mysqli_stmt_bind_param($updateStmt, "si", $customer, $userId);
                    mysqli_stmt_execute($updateStmt);
                    StripeLogger::log(StripeLogLevel::INFO, "STRIPE CUSTOMER ID UPDATED - User ID: $userId, Customer ID: $customer");
                }
            }
        }

        // If still no user found, we can't process this invoice
        if (!$userId) {
            StripeLogger::log(StripeLogLevel::WARNING, "NO USER FOUND FOR INVOICE - Customer ID: $customer, Subscription ID: $subscriptionId");
            return;
        }

        // Save invoice information to database
        $insertQuery = "INSERT INTO stripe_payment_history (
            user_id,
            stripe_customer_id,
            stripe_invoice_id,
            stripe_subscription_id,
            amount,
            amount_paid,
            currency,
            status,
            period_start,
            period_end,
            created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 'paid', ?, ?, ?)";

        $insertStmt = mysqli_prepare($link, $insertQuery);

        if (!$insertStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        // Calculate amount in dollars before binding
        $amountInDollars = $amount / 100; // Stripe amounts are in cents

        mysqli_stmt_bind_param(
            $insertStmt,
            "isssddssss",
            $userId,
            $customer,
            $invoiceId,
            $subscriptionId,
            $amountInDollars,  // amount
            $amountInDollars,  // amount_paid
            $currency,
            $periodStart,
            $periodEnd,
            $created
        );

        if (!mysqli_stmt_execute($insertStmt)) {
            throw new Exception("Failed to save invoice: " . mysqli_error($link));
        }

        // Update subscription status
        if ($subscriptionId) {
            $updateQuery = "UPDATE stripe_user_subscriptions SET
                status = 'active',
                updated_at = NOW()
                WHERE stripe_subscription_id = ?";

            $updateStmt = mysqli_prepare($link, $updateQuery);

            if ($updateStmt) {
                mysqli_stmt_bind_param($updateStmt, "s", $subscriptionId);
                mysqli_stmt_execute($updateStmt);
            }

            // Get subscription information
            $subscriptionQuery = "SELECT plan_name FROM stripe_user_subscriptions WHERE stripe_subscription_id = ?";
            $subscriptionStmt = mysqli_prepare($link, $subscriptionQuery);

            if ($subscriptionStmt) {
                mysqli_stmt_bind_param($subscriptionStmt, "s", $subscriptionId);
                mysqli_stmt_execute($subscriptionStmt);
                $subscriptionResult = mysqli_stmt_get_result($subscriptionStmt);
                $subscription = mysqli_fetch_assoc($subscriptionResult);

                if ($subscription) {
                    // Determine subscription level
                    $subscriptionLevel = determineSubscriptionLevel($subscription['plan_name']);

                    // Update user table
                    $updateUserQuery = "UPDATE users SET subscription_level = ? WHERE id = ?";
                    $updateUserStmt = mysqli_prepare($link, $updateUserQuery);

                    if ($updateUserStmt) {
                        mysqli_stmt_bind_param($updateUserStmt, "si", $subscriptionLevel, $userId);

                        if (mysqli_stmt_execute($updateUserStmt)) {
                            StripeLogger::log(StripeLogLevel::INFO, "STRIPE USER SUBSCRIPTION LEVEL UPDATED - User ID: $userId, New Level: $subscriptionLevel");
                        } else {
                            StripeLogger::log(StripeLogLevel::ERROR, "Failed to update user subscription level", ['error' => mysqli_error($link), 'userId' => $userId]);
                        }
                    }
                }
            }
        }

        StripeLogger::log(StripeLogLevel::INFO, "STRIPE INVOICE RECORDED - User ID: $userId, Invoice ID: $invoiceId, Amount: $amountInDollars $currency");

    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "STRIPE INVOICE PROCESSING ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'invoiceId' => $invoiceId ?? 'unknown',
            'customerId' => $customer ?? 'unknown'
        ]);
    }
}

/**
 * Handle failed invoice event
 *
 * @param object $invoice The invoice object from Stripe
 */
function handleFailedInvoice($invoice) {
    global $link;

    // Process failed invoice
    $invoiceId = $invoice->id;
    $customer = $invoice->customer;
    $subscriptionId = $invoice->subscription;
    $amount = $invoice->amount_due;
    $currency = $invoice->currency;
    $attempt_count = $invoice->attempt_count;
    $nextPaymentAttempt = $invoice->next_payment_attempt ? date('Y-m-d H:i:s', $invoice->next_payment_attempt) : null;
    $created = date('Y-m-d H:i:s', $invoice->created);

    StripeLogger::log(StripeLogLevel::WARNING, "STRIPE INVOICE FAILED - Customer: $customer, Invoice ID: $invoiceId, Attempt: $attempt_count");

    try {
        // Find user by stripe_customer_id
        $userId = null;

        // 1. First try to find user by stripe_customer_id
        $query = "SELECT id, email FROM users WHERE stripe_customer_id = ?";
        $stmt = mysqli_prepare($link, $query);

        if (!$stmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($stmt, "s", $customer);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $user = mysqli_fetch_assoc($result);

        if ($user) {
            $userId = $user['id'];
            StripeLogger::log(StripeLogLevel::DEBUG, "STRIPE USER FOUND BY CUSTOMER ID - User ID: $userId");
        }

        // 2. If user not found and subscription exists, try to find by subscription
        if (!$userId && $subscriptionId) {
            $query = "SELECT user_id FROM stripe_user_subscriptions WHERE stripe_subscription_id = ?";
            $stmt = mysqli_prepare($link, $query);

            if (!$stmt) {
                throw new Exception("Database error: " . mysqli_error($link));
            }

            mysqli_stmt_bind_param($stmt, "s", $subscriptionId);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $subscription = mysqli_fetch_assoc($result);

            if ($subscription) {
                $userId = $subscription['user_id'];
                StripeLogger::log(StripeLogLevel::DEBUG, "STRIPE USER FOUND BY SUBSCRIPTION ID - User ID: $userId");

                // Update user's stripe_customer_id if it's empty
                $updateQuery = "UPDATE users SET stripe_customer_id = ? WHERE id = ? AND (stripe_customer_id IS NULL OR stripe_customer_id = '')";
                $updateStmt = mysqli_prepare($link, $updateQuery);

                if ($updateStmt) {
                    mysqli_stmt_bind_param($updateStmt, "si", $customer, $userId);
                    mysqli_stmt_execute($updateStmt);
                    StripeLogger::log(StripeLogLevel::INFO, "STRIPE CUSTOMER ID UPDATED - User ID: $userId, Customer ID: $customer");
                }
            }
        }

        // 3. If still no user found, check checkout sessions
        if (!$userId && $subscriptionId) {
            $query = "SELECT client_reference_id FROM stripe_checkout_sessions WHERE subscription_id = ?";
            $stmt = mysqli_prepare($link, $query);

            if (!$stmt) {
                throw new Exception("Database error: " . mysqli_error($link));
            }

            mysqli_stmt_bind_param($stmt, "s", $subscriptionId);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $session = mysqli_fetch_assoc($result);

            if ($session && $session['client_reference_id']) {
                $userId = $session['client_reference_id'];
                StripeLogger::log(StripeLogLevel::DEBUG, "STRIPE USER FOUND BY CHECKOUT SESSION - User ID: $userId");

                // Update user's stripe_customer_id if it's empty
                $updateQuery = "UPDATE users SET stripe_customer_id = ? WHERE id = ? AND (stripe_customer_id IS NULL OR stripe_customer_id = '')";
                $updateStmt = mysqli_prepare($link, $updateQuery);

                if ($updateStmt) {
                    mysqli_stmt_bind_param($updateStmt, "si", $customer, $userId);
                    mysqli_stmt_execute($updateStmt);
                    StripeLogger::log(StripeLogLevel::INFO, "STRIPE CUSTOMER ID UPDATED - User ID: $userId, Customer ID: $customer");
                }
            }
        }

        // If still no user found, we can't process this invoice
        if (!$userId) {
            StripeLogger::log(StripeLogLevel::WARNING, "NO USER FOUND FOR FAILED INVOICE - Customer ID: $customer, Subscription ID: $subscriptionId");
            return;
        }

        // Save failed invoice information to database
        $insertQuery = "INSERT INTO stripe_payment_history (
            user_id,
            stripe_customer_id,
            stripe_invoice_id,
            stripe_subscription_id,
            amount,
            amount_due,
            currency,
            status,
            attempt_count,
            next_payment_attempt,
            created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 'failed', ?, ?, ?)";

        $insertStmt = mysqli_prepare($link, $insertQuery);

        if (!$insertStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        // Calculate amount in dollars before binding
        $amountInDollars = $amount / 100; // Stripe amounts are in cents

        mysqli_stmt_bind_param(
            $insertStmt,
            "isssddsis",
            $userId,
            $customer,
            $invoiceId,
            $subscriptionId,
            $amountInDollars,  // amount
            $amountInDollars,  // amount_due
            $currency,
            $attempt_count,
            $nextPaymentAttempt,
            $created
        );

        if (!mysqli_stmt_execute($insertStmt)) {
            throw new Exception("Failed to save failed invoice: " . mysqli_error($link));
        }

        // Update subscription status
        if ($subscriptionId) {
            $updateQuery = "UPDATE stripe_user_subscriptions SET
                status = 'past_due',
                updated_at = NOW()
                WHERE stripe_subscription_id = ?";

            $updateStmt = mysqli_prepare($link, $updateQuery);

            if ($updateStmt) {
                mysqli_stmt_bind_param($updateStmt, "s", $subscriptionId);
                mysqli_stmt_execute($updateStmt);
            }
        }

        StripeLogger::log(StripeLogLevel::WARNING, "STRIPE FAILED INVOICE RECORDED - User ID: $userId, Invoice ID: $invoiceId, Amount Due: $amountInDollars $currency");

        // Send notification to user (via email or other methods)
        // This part can be customized according to your notification system

    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "STRIPE FAILED INVOICE PROCESSING ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'invoiceId' => $invoiceId ?? 'unknown',
            'customerId' => $customer ?? 'unknown'
        ]);
    }
}

/**
 * Get user's invoice history
 */
function getUserInvoices() {
    global $link, $data;

    // Get user ID from JWT token
    $userId = authenticate_user_or_error();
    if ($userId === false) {
        return; // Error response already sent
    }

    $limit = isset($data['limit']) ? intval($data['limit']) : 10;
    $offset = isset($data['offset']) ? intval($data['offset']) : 0;

    try {
        // Get user's stripe customer ID
        $userQuery = "SELECT stripe_customer_id FROM users WHERE id = ?";
        $userStmt = mysqli_prepare($link, $userQuery);

        if (!$userStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($userStmt, "i", $userId);
        mysqli_stmt_execute($userStmt);
        $userResult = mysqli_stmt_get_result($userStmt);
        $user = mysqli_fetch_assoc($userResult);

        if (!$user || !$user['stripe_customer_id']) {
            echo json_encode(new SuccessResult([
                'invoices' => [],
                'total' => 0,
                'message' => 'User has no Stripe customer ID'
            ]));
            return;
        }

        $stripeCustomerId = $user['stripe_customer_id'];

        // Get total count for pagination - only count records with stripe_invoice_id
        $countQuery = "SELECT COUNT(*) as total FROM stripe_payment_history WHERE user_id = ? AND stripe_invoice_id IS NOT NULL";
        $countStmt = mysqli_prepare($link, $countQuery);

        if (!$countStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($countStmt, "i", $userId);
        mysqli_stmt_execute($countStmt);
        $countResult = mysqli_stmt_get_result($countStmt);
        $countRow = mysqli_fetch_assoc($countResult);
        $total = $countRow['total'];

        // Get user's invoice history from database - only get records with stripe_invoice_id
        $query = "SELECT * FROM stripe_payment_history
                  WHERE user_id = ? AND stripe_invoice_id IS NOT NULL
                  ORDER BY created_at DESC
                  LIMIT ? OFFSET ?";
        $stmt = mysqli_prepare($link, $query);

        if (!$stmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($stmt, "iii", $userId, $limit, $offset);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);

        $invoices = [];

        while ($invoice = mysqli_fetch_assoc($result)) {
            // Format invoice data
            // Store original amounts for debugging if needed
            $originalAmount = $invoice['amount'];
            $originalAmountPaid = $invoice['amount_paid'];
            $originalAmountDue = $invoice['amount_due'];

            // Format amounts to ensure they're in the correct format
            // If the amount is already in dollars format (e.g., 29.00), it will remain unchanged
            // If it's in cents format (e.g., 2900), it will be converted to dollars (29.00)
            $amount = $invoice['amount'];
            $amountPaid = $invoice['amount_paid'];
            $amountDue = $invoice['amount_due'];

            // If invoice_pdf_url is empty but we have a Stripe invoice id, fetch from Stripe and cache
            $invoicePdfUrl = isset($invoice['invoice_pdf_url']) ? $invoice['invoice_pdf_url'] : null;
            if ((empty($invoicePdfUrl) || $invoicePdfUrl === '') && !empty($invoice['stripe_invoice_id'])) {
                try {
                    $stripeInvoice = \Stripe\Invoice::retrieve($invoice['stripe_invoice_id']);
                    if ($stripeInvoice && isset($stripeInvoice->invoice_pdf) && !empty($stripeInvoice->invoice_pdf)) {
                        $invoicePdfUrl = $stripeInvoice->invoice_pdf;
                        // Persist fetched URL into our DB for future calls
                        $updateStmt = mysqli_prepare($link, "UPDATE stripe_payment_history SET invoice_pdf_url = ? WHERE id = ?");
                        if ($updateStmt) {
                            mysqli_stmt_bind_param($updateStmt, "si", $invoicePdfUrl, $invoice['id']);
                            mysqli_stmt_execute($updateStmt);
                        }
                        \StripeLogger::log(\StripeLogLevel::INFO, "Backfilled invoice_pdf_url from Stripe", [
                            'stripe_invoice_id' => $invoice['stripe_invoice_id'],
                            'db_id' => $invoice['id']
                        ]);
                    }
                } catch (\Exception $e) {
                    \StripeLogger::log(\StripeLogLevel::WARNING, "Failed to retrieve invoice from Stripe for PDF URL", [
                        'stripe_invoice_id' => $invoice['stripe_invoice_id'],
                        'error' => $e->getMessage()
                    ]);
                }
            }

            // Add formatted amounts to the response
            $invoices[] = [
                'id' => $invoice['id'],
                'stripe_invoice_id' => $invoice['stripe_invoice_id'],
                'stripe_subscription_id' => $invoice['stripe_subscription_id'],
                'amount' => $amount,
                'amount_paid' => $amountPaid,
                'amount_due' => $amountDue,
                'amount_raw' => $originalAmount, // Include raw amount for debugging
                'currency' => $invoice['currency'],
                'status' => $invoice['status'],
                'period_start' => $invoice['period_start'],
                'period_end' => $invoice['period_end'],
                'created_at' => $invoice['created_at'],
                'invoice_pdf_url' => $invoicePdfUrl
            ];
        }

        // Try to get additional invoices from Stripe if needed
        if (count($invoices) < $limit && $stripeCustomerId) {
            try {
                // Get invoices from Stripe API
                $stripeInvoices = \Stripe\Invoice::all([
                    'customer' => $stripeCustomerId,
                    'limit' => $limit
                ]);

                // Process Stripe invoices that might not be in our database
                foreach ($stripeInvoices->data as $stripeInvoice) {
                    $invoiceId = $stripeInvoice->id;

                    // Check if this invoice is already in our result set
                    $found = false;
                    foreach ($invoices as $existingInvoice) {
                        if (isset($existingInvoice['stripe_invoice_id']) && $existingInvoice['stripe_invoice_id'] === $invoiceId) {
                            $found = true;
                            break;
                        }
                    }

                    // If not found, add it to the result
                    if (!$found) {
                        // Store original amounts for debugging
                        $originalAmount = $stripeInvoice->amount_due;

                        // Convert from cents to dollars
                        $amount = $stripeInvoice->amount_due / 100;
                        $amountPaid = $stripeInvoice->amount_paid / 100;
                        $amountDue = $stripeInvoice->amount_due / 100;

                        $invoices[] = [
                            'id' => null, // Not in our database
                            'stripe_invoice_id' => $invoiceId,
                            'stripe_subscription_id' => $stripeInvoice->subscription,
                            'amount' => $amount,
                            'amount_paid' => $amountPaid,
                            'amount_due' => $amountDue,
                            'amount_raw' => $originalAmount, // Include raw amount for debugging
                            'currency' => $stripeInvoice->currency,
                            'status' => $stripeInvoice->status,
                            'period_start' => date('Y-m-d H:i:s', $stripeInvoice->period_start),
                            'period_end' => date('Y-m-d H:i:s', $stripeInvoice->period_end),
                            'created_at' => date('Y-m-d H:i:s', $stripeInvoice->created),
                            'invoice_pdf_url' => $stripeInvoice->invoice_pdf ?? null,
                            'from_stripe_api' => true
                        ];
                    }
                }

                // Sort invoices by created_at
                usort($invoices, function($a, $b) {
                    return strtotime($b['created_at']) - strtotime($a['created_at']);
                });

                // Limit to requested number
                $invoices = array_slice($invoices, 0, $limit);

            } catch (\Exception $e) {
                // If Stripe API call fails, just use database results
                StripeLogger::log(StripeLogLevel::WARNING, "Error retrieving Stripe invoices: " . $e->getMessage(), [
                    'exception' => get_class($e),
                    'customerId' => $stripeCustomerId
                ]);
            }
        }

        // Return invoice history
        echo json_encode(new SuccessResult([
            'invoices' => $invoices,
            'total' => $total,
            'limit' => $limit,
            'offset' => $offset
        ]));
    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "GET USER INVOICES ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'userId' => $userId ?? 'unknown'
        ]);
        echo json_encode(new ErrorResult($e->getMessage(), 500));
    }
}

/**
 * Handle invoice updated event
 *
 * @param object $invoice The invoice object from Stripe
 */
function handleInvoiceUpdated($invoice) {
    global $link;

    // Process updated invoice
    $invoiceId = $invoice->id;
    $amount = $invoice->amount_due;
    $currency = $invoice->currency;
    $customer = $invoice->customer;
    $subscriptionId = $invoice->subscription;
    $status = $invoice->status;
    $created = date('Y-m-d H:i:s', $invoice->created);
    $periodStart = date('Y-m-d H:i:s', $invoice->period_start);
    $periodEnd = date('Y-m-d H:i:s', $invoice->period_end);

    StripeLogger::log(StripeLogLevel::INFO, "STRIPE INVOICE UPDATED - Amount: $amount $currency - Customer: $customer, Invoice ID: $invoiceId, Status: $status");

    try {
        // Find user by stripe_customer_id
        $userId = null;

        // 1. First try to find user by stripe_customer_id
        $query = "SELECT id FROM users WHERE stripe_customer_id = ?";
        $stmt = mysqli_prepare($link, $query);

        if (!$stmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($stmt, "s", $customer);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $user = mysqli_fetch_assoc($result);

        if ($user) {
            $userId = $user['id'];
            StripeLogger::log(StripeLogLevel::DEBUG, "STRIPE USER FOUND BY CUSTOMER ID - User ID: $userId");
        }

        // 2. If user not found and subscription exists, try to find by subscription
        if (!$userId && $subscriptionId) {
            $query = "SELECT user_id FROM stripe_user_subscriptions WHERE stripe_subscription_id = ?";
            $stmt = mysqli_prepare($link, $query);

            if (!$stmt) {
                throw new Exception("Database error: " . mysqli_error($link));
            }

            mysqli_stmt_bind_param($stmt, "s", $subscriptionId);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $subscription = mysqli_fetch_assoc($result);

            if ($subscription) {
                $userId = $subscription['user_id'];
                StripeLogger::log(StripeLogLevel::DEBUG, "STRIPE USER FOUND BY SUBSCRIPTION ID - User ID: $userId");
            }
        }

        // If no user found, we can't process this invoice
        if (!$userId) {
            StripeLogger::log(StripeLogLevel::WARNING, "NO USER FOUND FOR UPDATED INVOICE - Customer ID: $customer, Subscription ID: $subscriptionId");
            return;
        }

        // Check if invoice already exists in our database
        $checkQuery = "SELECT id FROM stripe_payment_history WHERE stripe_invoice_id = ?";
        $checkStmt = mysqli_prepare($link, $checkQuery);

        if (!$checkStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($checkStmt, "s", $invoiceId);
        mysqli_stmt_execute($checkStmt);
        $checkResult = mysqli_stmt_get_result($checkStmt);
        $existingInvoice = mysqli_fetch_assoc($checkResult);

        // Calculate amount in dollars
        $amountInDollars = $amount / 100; // Stripe amounts are in cents

        if ($existingInvoice) {
            // Update existing invoice record
            $updateQuery = "UPDATE stripe_payment_history SET
                amount = ?,
                amount_due = ?,
                currency = ?,
                status = ?,
                period_start = ?,
                period_end = ?,
                updated_at = NOW()
                WHERE stripe_invoice_id = ?";

            $updateStmt = mysqli_prepare($link, $updateQuery);

            if (!$updateStmt) {
                throw new Exception("Database error: " . mysqli_error($link));
            }

            mysqli_stmt_bind_param(
                $updateStmt,
                "ddsssss",
                $amountInDollars,
                $amountInDollars,
                $currency,
                $status,
                $periodStart,
                $periodEnd,
                $invoiceId
            );

            if (!mysqli_stmt_execute($updateStmt)) {
                throw new Exception("Failed to update invoice: " . mysqli_error($link));
            }

            StripeLogger::log(StripeLogLevel::INFO, "STRIPE INVOICE UPDATED IN DB - Invoice ID: $invoiceId, New Amount: $amountInDollars $currency");
        } else {
            // This is an update for an invoice we don't have in our database yet
            // We'll insert it as a new record
            $insertQuery = "INSERT INTO stripe_payment_history (
                user_id,
                stripe_customer_id,
                stripe_invoice_id,
                stripe_subscription_id,
                amount,
                amount_due,
                currency,
                status,
                period_start,
                period_end,
                created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            $insertStmt = mysqli_prepare($link, $insertQuery);

            if (!$insertStmt) {
                throw new Exception("Database error: " . mysqli_error($link));
            }

            mysqli_stmt_bind_param(
                $insertStmt,
                "isssddssss",
                $userId,
                $customer,
                $invoiceId,
                $subscriptionId,
                $amountInDollars,
                $amountInDollars,
                $currency,
                $status,
                $periodStart,
                $periodEnd,
                $created
            );

            if (!mysqli_stmt_execute($insertStmt)) {
                throw new Exception("Failed to save invoice: " . mysqli_error($link));
            }

            StripeLogger::log(StripeLogLevel::INFO, "STRIPE INVOICE ADDED TO DB - Invoice ID: $invoiceId, Amount: $amountInDollars $currency");
        }

        // If this is a subscription-related invoice and it's finalized, update subscription info
        if ($subscriptionId && ($status === 'paid' || $status === 'open')) {
            // Get subscription details from Stripe to ensure we have the latest info
            try {
                $subscription = \Stripe\Subscription::retrieve($subscriptionId);

                if ($subscription && isset($subscription->items->data[0]->price->product)) {
                    $productId = $subscription->items->data[0]->price->product;

                    // Get product details to determine plan name
                    $product = \Stripe\Product::retrieve($productId);

                    if ($product) {
                        $planName = $product->name;

                        // Update subscription record with latest plan info
                        $updateSubQuery = "UPDATE stripe_user_subscriptions SET
                            plan_name = ?,
                            updated_at = NOW()
                            WHERE stripe_subscription_id = ?";

                        $updateSubStmt = mysqli_prepare($link, $updateSubQuery);

                        if ($updateSubStmt) {
                            mysqli_stmt_bind_param($updateSubStmt, "ss", $planName, $subscriptionId);

                            if (mysqli_stmt_execute($updateSubStmt)) {
                                StripeLogger::log(StripeLogLevel::INFO, "STRIPE SUBSCRIPTION PLAN UPDATED - Subscription ID: $subscriptionId, New Plan: $planName");

                                // Update user's subscription level
                                $subscriptionLevel = determineSubscriptionLevel($planName);

                                $updateUserQuery = "UPDATE users SET
                                    subscription_level = ?
                                    WHERE id = ?";

                                $updateUserStmt = mysqli_prepare($link, $updateUserQuery);

                                if ($updateUserStmt) {
                                    mysqli_stmt_bind_param($updateUserStmt, "si", $subscriptionLevel, $userId);

                                    if (mysqli_stmt_execute($updateUserStmt)) {
                                        StripeLogger::log(StripeLogLevel::INFO, "USER SUBSCRIPTION LEVEL UPDATED - User ID: $userId, New Level: $subscriptionLevel");
                                    }
                                }
                            }
                        }
                    }
                }
            } catch (Exception $e) {
                StripeLogger::log(StripeLogLevel::ERROR, "STRIPE SUBSCRIPTION RETRIEVAL ERROR: " . $e->getMessage(), [
                    'exception' => get_class($e),
                    'subscriptionId' => $subscriptionId
                ]);
            }
        }

    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "STRIPE INVOICE UPDATE PROCESSING ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'invoiceId' => $invoiceId ?? 'unknown',
            'customerId' => $customer ?? 'unknown'
        ]);
    }
}

/**
 * Handle invoice finalized event
 *
 * @param object $invoice The invoice object from Stripe
 */
function handleInvoiceFinalized($invoice) {
    global $link;

    // Process finalized invoice
    $invoiceId = $invoice->id;
    $customer = $invoice->customer;
    $subscriptionId = $invoice->subscription;
    $invoicePdfUrl = $invoice->invoice_pdf ?? null;
    $hostedInvoiceUrl = $invoice->hosted_invoice_url ?? null;
    $amount = $invoice->amount_due;
    $currency = $invoice->currency;
    $status = $invoice->status;
    $created = date('Y-m-d H:i:s', $invoice->created);
    $periodStart = date('Y-m-d H:i:s', $invoice->period_start);
    $periodEnd = date('Y-m-d H:i:s', $invoice->period_end);

    StripeLogger::log(StripeLogLevel::INFO, "STRIPE INVOICE FINALIZED - Customer: $customer, Invoice ID: $invoiceId, PDF URL: $invoicePdfUrl");

    try {
        // Find user by stripe_customer_id
        $userId = null;

        // 1. First try to find user by stripe_customer_id
        $query = "SELECT id FROM users WHERE stripe_customer_id = ?";
        $stmt = mysqli_prepare($link, $query);

        if (!$stmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($stmt, "s", $customer);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $user = mysqli_fetch_assoc($result);

        if ($user) {
            $userId = $user['id'];
            StripeLogger::log(StripeLogLevel::DEBUG, "STRIPE USER FOUND BY CUSTOMER ID - User ID: $userId");
        }

        // 2. If user not found and subscription exists, try to find by subscription
        if (!$userId && $subscriptionId) {
            $query = "SELECT user_id FROM stripe_user_subscriptions WHERE stripe_subscription_id = ?";
            $stmt = mysqli_prepare($link, $query);

            if (!$stmt) {
                throw new Exception("Database error: " . mysqli_error($link));
            }

            mysqli_stmt_bind_param($stmt, "s", $subscriptionId);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $subscription = mysqli_fetch_assoc($result);

            if ($subscription) {
                $userId = $subscription['user_id'];
                StripeLogger::log(StripeLogLevel::DEBUG, "STRIPE USER FOUND BY SUBSCRIPTION ID - User ID: $userId");

                // Update user's stripe_customer_id if it's empty
                $updateQuery = "UPDATE users SET stripe_customer_id = ? WHERE id = ? AND (stripe_customer_id IS NULL OR stripe_customer_id = '')";
                $updateStmt = mysqli_prepare($link, $updateQuery);

                if ($updateStmt) {
                    mysqli_stmt_bind_param($updateStmt, "si", $customer, $userId);
                    mysqli_stmt_execute($updateStmt);
                    StripeLogger::log(StripeLogLevel::INFO, "STRIPE CUSTOMER ID UPDATED - User ID: $userId, Customer ID: $customer");
                }
            }
        }

        // 3. If still no user found, check checkout sessions
        if (!$userId && $subscriptionId) {
            $query = "SELECT client_reference_id FROM stripe_checkout_sessions WHERE subscription_id = ?";
            $stmt = mysqli_prepare($link, $query);

            if (!$stmt) {
                throw new Exception("Database error: " . mysqli_error($link));
            }

            mysqli_stmt_bind_param($stmt, "s", $subscriptionId);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $session = mysqli_fetch_assoc($result);

            if ($session && $session['client_reference_id']) {
                $userId = $session['client_reference_id'];
                StripeLogger::log(StripeLogLevel::DEBUG, "STRIPE USER FOUND BY CHECKOUT SESSION - User ID: $userId");

                // Update user's stripe_customer_id if it's empty
                $updateQuery = "UPDATE users SET stripe_customer_id = ? WHERE id = ? AND (stripe_customer_id IS NULL OR stripe_customer_id = '')";
                $updateStmt = mysqli_prepare($link, $updateQuery);

                if ($updateStmt) {
                    mysqli_stmt_bind_param($updateStmt, "si", $customer, $userId);
                    mysqli_stmt_execute($updateStmt);
                    StripeLogger::log(StripeLogLevel::INFO, "STRIPE CUSTOMER ID UPDATED - User ID: $userId, Customer ID: $customer");
                }
            }
        }

        // If no user found, we can't process this invoice
        if (!$userId) {
            StripeLogger::log(StripeLogLevel::WARNING, "NO USER FOUND FOR FINALIZED INVOICE - Customer ID: $customer, Subscription ID: $subscriptionId");
            return;
        }

        // Check if invoice already exists in our database
        $checkQuery = "SELECT id FROM stripe_payment_history WHERE stripe_invoice_id = ?";
        $checkStmt = mysqli_prepare($link, $checkQuery);

        if (!$checkStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($checkStmt, "s", $invoiceId);
        mysqli_stmt_execute($checkStmt);
        $checkResult = mysqli_stmt_get_result($checkStmt);
        $existingInvoice = mysqli_fetch_assoc($checkResult);

        // Calculate amount in dollars
        $amountInDollars = $amount / 100; // Stripe amounts are in cents

        if ($existingInvoice) {
            // Update existing invoice record with PDF URL
            $updateQuery = "UPDATE stripe_payment_history SET
                invoice_pdf_url = ?,
                amount = ?,
                amount_due = ?,
                currency = ?,
                status = ?,
                period_start = ?,
                period_end = ?,
                updated_at = NOW()
                WHERE stripe_invoice_id = ?";

            $updateStmt = mysqli_prepare($link, $updateQuery);

            if (!$updateStmt) {
                throw new Exception("Database error: " . mysqli_error($link));
            }

            mysqli_stmt_bind_param(
                $updateStmt,
                "sddsssss",
                $invoicePdfUrl,
                $amountInDollars,
                $amountInDollars,
                $currency,
                $status,
                $periodStart,
                $periodEnd,
                $invoiceId
            );

            if (!mysqli_stmt_execute($updateStmt)) {
                throw new Exception("Failed to update invoice with PDF URL: " . mysqli_error($link));
            }

            StripeLogger::log(StripeLogLevel::INFO, "STRIPE INVOICE PDF URL UPDATED - Invoice ID: $invoiceId, PDF URL: $invoicePdfUrl");
        } else {
            // This is a finalized invoice we don't have in our database yet
            // We'll insert it as a new record with PDF URL
            $insertQuery = "INSERT INTO stripe_payment_history (
                user_id,
                stripe_customer_id,
                stripe_invoice_id,
                stripe_subscription_id,
                invoice_pdf_url,
                amount,
                amount_due,
                currency,
                status,
                period_start,
                period_end,
                created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            $insertStmt = mysqli_prepare($link, $insertQuery);

            if (!$insertStmt) {
                throw new Exception("Database error: " . mysqli_error($link));
            }

            mysqli_stmt_bind_param(
                $insertStmt,
                "issssddsssss",
                $userId,
                $customer,
                $invoiceId,
                $subscriptionId,
                $invoicePdfUrl,
                $amountInDollars,
                $amountInDollars,
                $currency,
                $status,
                $periodStart,
                $periodEnd,
                $created
            );

            if (!mysqli_stmt_execute($insertStmt)) {
                throw new Exception("Failed to save finalized invoice: " . mysqli_error($link));
            }

            StripeLogger::log(StripeLogLevel::INFO, "STRIPE FINALIZED INVOICE ADDED TO DB - Invoice ID: $invoiceId, PDF URL: $invoicePdfUrl");
        }

    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "STRIPE INVOICE FINALIZED PROCESSING ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'invoiceId' => $invoiceId ?? 'unknown',
            'customerId' => $customer ?? 'unknown'
        ]);
    }
}
