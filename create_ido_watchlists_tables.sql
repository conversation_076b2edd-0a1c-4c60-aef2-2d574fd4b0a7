-- <PERSON>QL script to create the ido_watchlists and ido_watchlist_projects tables

-- Create the ido_watchlists table
CREATE TABLE IF NOT EXISTS ido_watchlists (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    icon_id INT DEFAULT 1,
    description TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX (user_id)
);

-- Create the ido_watchlist_projects table
CREATE TABLE IF NOT EXISTS ido_watchlist_projects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    watchlist_id INT NOT NULL,
    ido_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_watchlist_ido (watchlist_id, ido_id),
    INDEX (watchlist_id),
    <PERSON>DE<PERSON> (ido_id),
    <PERSON><PERSON><PERSON><PERSON><PERSON>Y (watchlist_id) REFERENCES ido_watchlists(id) ON DELETE CASCADE
);

-- Add comments to explain the table structure
-- ido_watchlists:
-- id: Primary key
-- user_id: The ID of the user who owns the watchlist
-- name: The name of the watchlist
-- icon_id: The ID of the icon for the watchlist
-- description: Optional description of the watchlist
-- created_at: When the watchlist was created
-- updated_at: When the watchlist was last updated

-- ido_watchlist_projects:
-- id: Primary key
-- watchlist_id: The ID of the watchlist
-- ido_id: The ID of the IDO project
-- created_at: When the project was added to the watchlist
-- unique_watchlist_ido: Ensures a project can only be added once to a watchlist
-- Foreign key ensures that when a watchlist is deleted, all its projects are also deleted
