-- System Logs Tablosu
-- Bu tablo StripeLogger ve diğer sistem logları için kullanı<PERSON>k
-- Stripe event logları da dahil olmak üzere tüm log türlerini destekler

CREATE TABLE `system_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `log_level` varchar(20) NOT NULL COMMENT 'Log seviyesi: debug, info, notice, warning, error, critical, alert, emergency',
  `message` text NOT NULL COMMENT 'Log mesajı',
  `context` longtext DEFAULT NULL COMMENT 'JSON formatında ek veriler (context, event_type, event_id vb.)',
  `log_type` varchar(50) NOT NULL DEFAULT 'general' COMMENT 'Log türü: general, stripe_event, authentication, payment vb.',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp() COMMENT 'Log kaydının oluşturulma zamanı',
  PRIMARY KEY (`id`),
  KEY `idx_log_level` (`log_level`),
  KEY `idx_log_type` (`log_type`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_level_type_date` (`log_level`, `log_type`, `created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Sistem logları tablosu - StripeLogger ve diğer sistem logları için';

-- Örnek kullanım:
-- INSERT INTO system_logs (log_level, message, context, log_type) 
-- VALUES ('info', 'User login successful', '{"user_id": 123, "ip": "***********"}', 'authentication');

-- INSERT INTO system_logs (log_level, message, context, log_type) 
-- VALUES ('error', 'Payment failed', '{"event_type": "payment_intent.payment_failed", "event_id": "pi_123", "amount": 1000}', 'stripe_event');
