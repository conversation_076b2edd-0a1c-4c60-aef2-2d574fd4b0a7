-- Migration script to update user_alerts table for separate above/below triggers
-- This script updates the existing table structure to support independent above/below triggering

-- Add new columns for separate trigger tracking
ALTER TABLE user_alerts 
ADD COLUMN IF NOT EXISTS is_triggered_above BOOLEAN DEFAULT FALSE AFTER price_below,
ADD COLUMN IF NOT EXISTS is_triggered_below BOOLEAN DEFAULT FALSE AFTER is_triggered_above,
ADD COLUMN IF NOT EXISTS triggered_above_at TIMESTAMP NULL AFTER is_triggered_below,
ADD COLUMN IF NOT EXISTS triggered_below_at TIMESTAMP NULL AFTER triggered_above_at;

-- Migrate existing data if old columns exist
-- If an alert was previously triggered, mark both conditions as triggered
UPDATE user_alerts 
SET 
    is_triggered_above = CASE 
        WHEN is_triggered = 1 AND condition_above = 1 THEN 1 
        ELSE 0 
    END,
    is_triggered_below = CASE 
        WHEN is_triggered = 1 AND condition_below = 1 THEN 1 
        ELSE 0 
    END,
    triggered_above_at = CASE 
        WHEN is_triggered = 1 AND condition_above = 1 THEN triggered_at 
        ELSE NULL 
    END,
    triggered_below_at = CASE 
        WHEN is_triggered = 1 AND condition_below = 1 THEN triggered_at 
        ELSE NULL 
    END
WHERE is_triggered IS NOT NULL;

-- Drop old columns (uncomment these lines after verifying the migration worked)
-- ALTER TABLE user_alerts DROP COLUMN IF EXISTS is_triggered;
-- ALTER TABLE user_alerts DROP COLUMN IF EXISTS triggered_at;

-- Verify the migration
SELECT 
    COUNT(*) as total_alerts,
    SUM(CASE WHEN condition_above = 1 THEN 1 ELSE 0 END) as above_conditions,
    SUM(CASE WHEN condition_below = 1 THEN 1 ELSE 0 END) as below_conditions,
    SUM(CASE WHEN is_triggered_above = 1 THEN 1 ELSE 0 END) as triggered_above,
    SUM(CASE WHEN is_triggered_below = 1 THEN 1 ELSE 0 END) as triggered_below
FROM user_alerts;
