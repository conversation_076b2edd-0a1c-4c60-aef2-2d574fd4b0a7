CREATE TABLE ico_metric_subgroups (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    metric_group INT NOT NULL,
    value INT NOT NULL,
    isactive TINYINT NOT NULL DEFAULT 1,
    excellent_text TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    good_text TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    fair_text TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    bad_text TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    poor_text TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    scoring_text TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    importance_text TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    how_score_text TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    conclusion_text TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    test_text TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    description_AR TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    description_DE TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    description_EN TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    description_ES TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    description_FR TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    description_HI TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    description_ID TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    description_IT TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    description_JA TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    description_KO TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    description_PT TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    description_RU TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    description_TR TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    description_VI TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    description_ZH TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    name_AR TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    name_DE TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    name_EN TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    name_ES TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    name_FR TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    name_HI TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    name_ID TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    name_IT TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    name_JA TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    name_KO TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    name_PT TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    name_RU TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    name_TR TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    name_VI TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    name_ZH TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci


) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
