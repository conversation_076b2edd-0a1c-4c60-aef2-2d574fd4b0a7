<?php

/**
 * Stripe Payment Method Functions
 *
 * This file contains functions related to Stripe payment methods.
 */
// Include required files
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../secrets.php';
require_once __DIR__ . '/../utils.php';
require_once __DIR__ . '/../models/ResultModel.php';
require_once __DIR__ . '/StripeLogger.php';
// Set Stripe API key
\Stripe\Stripe::setApiKey($stripeSecretKey);
/**
 * Handle payment method attached event
 *
 * @param object $paymentMethod The payment method object from Stripe
 */
function handlePaymentMethodAttached($paymentMethod)
{
    global $link;
    // Get relevant information
    $paymentMethodId = $paymentMethod->id;
    $customerId = $paymentMethod->customer;
    $type = $paymentMethod->type;
    $created = date('Y-m-d H:i:s', $paymentMethod->created);
    StripeLogger::log(StripeLogLevel::INFO, "STRIPE PAYMENT METHOD ATTACHED - ID: $paymentMethodId, Customer: $customerId, Type: $type");
    try {
        // Find user by stripe_customer_id
        $query = "SELECT id FROM users WHERE stripe_customer_id = ?";
        $stmt = mysqli_prepare($link, $query);
        if (!$stmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }
        mysqli_stmt_bind_param($stmt, "s", $customerId);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $user = mysqli_fetch_assoc($result);
        if (!$user) {
            StripeLogger::log(StripeLogLevel::WARNING, "No user found for customer ID: $customerId");
            return;
        }
        $userId = $user['id'];
        // Check if we want to store payment methods in the database
        // This is optional and depends on your application's needs
        // If you want to store payment methods, you can create a table like:
        /*
        CREATE TABLE IF NOT EXISTS stripe_payment_methods (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            stripe_customer_id VARCHAR(255) NOT NULL,
            stripe_payment_method_id VARCHAR(255) NOT NULL UNIQUE,
            type VARCHAR(50) NOT NULL,
            last4 VARCHAR(4),
            brand VARCHAR(50),
            exp_month INT,
            exp_year INT,
            is_default BOOLEAN DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX (user_id),
            INDEX (stripe_customer_id)
        );
        */
        // For now, just log the event
        StripeLogger::log(StripeLogLevel::INFO, "Payment method attached for user ID: $userId, Payment Method ID: $paymentMethodId");
        // You can also update the user's default payment method if needed
        // This is useful if this is the user's first payment method
        // Or if you want to set a specific payment method as default
    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "STRIPE PAYMENT METHOD ATTACHED ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'paymentMethodId' => $paymentMethodId ?? 'unknown',
            'customerId' => $customerId ?? 'unknown'
        ]);
    }
}
/**
 * Handle payment method detached event
 *
 * @param object $paymentMethod The payment method object from Stripe
 */
function handlePaymentMethodDetached($paymentMethod)
{
    global $link;
    // Get relevant information
    $paymentMethodId = $paymentMethod->id;
    $type = $paymentMethod->type;
    $created = date('Y-m-d H:i:s', $paymentMethod->created);
    StripeLogger::log(StripeLogLevel::WARNING, "STRIPE PAYMENT METHOD DETACHED - ID: $paymentMethodId, Type: $type");
    try {
        // Note: When a payment method is detached, the customer field is no longer available
        // So we need to find the user by payment method ID if we've stored it
        // If you're storing payment methods in the database, you can delete the record:
        /*
        $query = "DELETE FROM stripe_payment_methods WHERE stripe_payment_method_id = ?";
        $stmt = mysqli_prepare($link, $query);
        if (!$stmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }
        mysqli_stmt_bind_param($stmt, "s", $paymentMethodId);
        mysqli_stmt_execute($stmt);
        $affectedRows = mysqli_affected_rows($link);
        if ($affectedRows > 0) {
            StripeLogger::log(StripeLogLevel::INFO, "Payment method removed from database: $paymentMethodId");
        } else {
            StripeLogger::log(StripeLogLevel::NOTICE, "Payment method not found in database: $paymentMethodId");
        }
        */
        // For now, just log the event
        StripeLogger::log(StripeLogLevel::INFO, "Payment method detached: $paymentMethodId");
    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "STRIPE PAYMENT METHOD DETACHED ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'paymentMethodId' => $paymentMethodId ?? 'unknown'
        ]);
    }
}
/**
 * Get user's payment methods
 */
function getUserPaymentMethod()
{
    global $link, $data;
    // Get user ID from JWT token
    $userId = authenticate_user_or_error();
    if ($userId === false) {
        return; // Error response already sent
    }
    try {
        // Get user's stripe customer ID
        $userQuery = "SELECT stripe_customer_id FROM users WHERE id = ?";
        $userStmt = mysqli_prepare($link, $userQuery);
        if (!$userStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }
        mysqli_stmt_bind_param($userStmt, "i", $userId);
        mysqli_stmt_execute($userStmt);
        $userResult = mysqli_stmt_get_result($userStmt);
        $user = mysqli_fetch_assoc($userResult);
        if (!$user || !$user['stripe_customer_id']) {
            echo json_encode(new SuccessResult([
                'payment_methods' => [],
                'message' => 'User has no Stripe customer ID'
            ]));
            return;
        }
        $stripeCustomerId = $user['stripe_customer_id'];
        // Get payment methods from Stripe API
        $paymentMethods = \Stripe\PaymentMethod::all([
            'customer' => $stripeCustomerId,
            'type' => 'card'
        ]);
        $formattedPaymentMethods = [];
        foreach ($paymentMethods->data as $paymentMethod) {
            $card = $paymentMethod->card;
            $formattedPaymentMethods[] = [
                'id' => $paymentMethod->id,
                'type' => $paymentMethod->type,
                'card' => [
                    'brand' => $card->brand,
                    'last4' => $card->last4,
                    'exp_month' => $card->exp_month,
                    'exp_year' => $card->exp_year,
                    'country' => $card->country,
                    'funding' => $card->funding
                ],
                'created' => date('Y-m-d H:i:s', $paymentMethod->created),
                'is_default' => $paymentMethod->metadata->is_default ?? false
            ];
        }
        // Return payment methods
        echo json_encode(new SuccessResult([
            'payment_methods' => $formattedPaymentMethods
        ]));
    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "GET USER PAYMENT METHODS ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'userId' => $userId ?? 'unknown'
        ]);
        echo json_encode(new ErrorResult($e->getMessage(), 500));
    }
}
/**
 * Create a session for updating payment method
 */
function createPaymentUpdateSession()
{
    global $link, $data;
    // Get user ID from JWT token
    $userId = authenticate_user_or_error();
    if ($userId === false) {
        return; // Error response already sent
    }
    //TODO bu urller sabit olacak
    $successUrl = isset($data['successUrl']) ? $data['successUrl'] : 'https://coinscout.app/account/payment-success';
    $cancelUrl = isset($data['cancelUrl']) ? $data['cancelUrl'] : 'https://coinscout.app/account/payment-cancel';
    try {
        // Get user's stripe customer ID
        $userQuery = "SELECT stripe_customer_id, email FROM users WHERE id = ?";
        $userStmt = mysqli_prepare($link, $userQuery);
        if (!$userStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }
        mysqli_stmt_bind_param($userStmt, "i", $userId);
        mysqli_stmt_execute($userStmt);
        $userResult = mysqli_stmt_get_result($userStmt);
        $user = mysqli_fetch_assoc($userResult);
        if (!$user || !$user['stripe_customer_id']) {
            throw new Exception("User has no Stripe customer ID");
        }
        $stripeCustomerId = $user['stripe_customer_id'];
        $customerEmail = $user['email'];
        // Create a SetupIntent
        $session = \Stripe\Checkout\Session::create([
            'payment_method_types' => ['card'],
            'mode' => 'setup',
            'customer' => $stripeCustomerId,
            'success_url' => $successUrl,
            'cancel_url' => $cancelUrl,
            'client_reference_id' => $userId,
            'metadata' => [
                'user_id' => $userId
            ]
        ]);
        // Return session ID
        echo json_encode(new SuccessResult([
            'sessionId' => $session->id,
            'url' => $session->url
        ]));
    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "CREATE PAYMENT UPDATE SESSION ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'userId' => $userId ?? 'unknown'
        ]);
        echo json_encode(new ErrorResult($e->getMessage(), 500));
    }
}
