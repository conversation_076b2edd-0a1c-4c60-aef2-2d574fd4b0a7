/**
 * CoinScout Alert Websocket Client
 *
 * <PERSON><PERSON>, CoinScout websocket sunucusuna bağlanarak gerçek zamanlı
 * alert bildirimlerini almak için kullanılır.
 */
class AlertWebsocketClient {
/**
 * @param {string} serverUrl - Websocket sunucu URL'si (örn: wss://api.coinscout.app/ws)
     * @param {string} authToken - Kullanıcı JWT token'ı
     * @param {Object} options - Ek seçenekler
     */
    constructor(serverUrl, authToken, options = {}) {
        this.serverUrl = serverUrl;
        this.authToken = authToken;
        this.options = {
            reconnectInterval: 5000, // Yeniden bağlanma aralığı (ms)
            maxReconnectAttempts: 10, // Maksimum yeniden bağlanma denemesi
            debug: false,
            ...options
        };

        this.socket = null;
        this.isConnected = false;
        this.isAuthenticated = false;
        this.reconnectAttempts = 0;
        this.eventListeners = {
            'connect': [],
            'disconnect': [],
            'auth_success': [],
            'auth_error': [],
            'alert': [],
            'unread_notifications': [],
            'error': []
        };

        // Otomatik bağlantı
        if (this.options.autoConnect !== false) {
            this.connect();
        }
    }

    /**
     * Websocket sunucusuna bağlanır
     */
    connect() {
        if (this.socket) {
            this.disconnect();
        }

        try {
            this._debug('Websocket sunucusuna bağlanılıyor:', this.serverUrl);
            this.socket = new WebSocket(this.serverUrl);

            this.socket.onopen = () => {
                this._debug('Websocket bağlantısı açıldı');
                this.isConnected = true;
                this.reconnectAttempts = 0;
                this._triggerEvent('connect');

                // Kimlik doğrulama
                this.authenticate();
            };

            this.socket.onmessage = (event) => {
                this._handleMessage(event.data);
            };

            this.socket.onclose = () => {
                this._debug('Websocket bağlantısı kapandı');
                this.isConnected = false;
                this.isAuthenticated = false;
                this._triggerEvent('disconnect');

                // Yeniden bağlanma
                this._reconnect();
            };

            this.socket.onerror = (error) => {
                this._debug('Websocket hatası:', error);
                this._triggerEvent('error', error);
            };
        } catch (error) {
            this._debug('Bağlantı hatası:', error);
            this._triggerEvent('error', error);
            this._reconnect();
        }
    }

    /**
     * Websocket bağlantısını kapatır
     */
    disconnect() {
        if (this.socket) {
            this.socket.close();
            this.socket = null;
        }
        this.isConnected = false;
        this.isAuthenticated = false;
    }

    /**
     * Kullanıcı kimliğini doğrular
     */
    authenticate() {
        if (!this.isConnected) {
            this._debug('Kimlik doğrulama başarısız: Bağlantı yok');
            return;
        }

        this._debug('Kimlik doğrulama yapılıyor');
        this.socket.send(JSON.stringify({
            type: 'auth',
            token: this.authToken
        }));
    }

    /**
     * Belirtilen olay için bir dinleyici ekler
     *
     * @param {string} event - Olay adı ('connect', 'disconnect', 'auth_success', 'auth_error', 'alert', 'error')
     * @param {Function} callback - Olay gerçekleştiğinde çağrılacak fonksiyon
     */
    on(event, callback) {
        if (this.eventListeners[event]) {
            this.eventListeners[event].push(callback);
        }
        return this;
    }

    /**
     * Belirtilen olay için bir dinleyiciyi kaldırır
     *
     * @param {string} event - Olay adı
     * @param {Function} callback - Kaldırılacak dinleyici fonksiyonu
     */
    off(event, callback) {
        if (this.eventListeners[event]) {
            this.eventListeners[event] = this.eventListeners[event].filter(cb => cb !== callback);
        }
        return this;
    }

    /**
     * Gelen mesajları işler
     *
     * @private
     * @param {string} data - JSON formatında mesaj
     */
    _handleMessage(data) {
        try {
            const message = JSON.parse(data);
            this._debug('Mesaj alındı:', message);

            if (message.type === 'auth_response') {
                if (message.status === 'success') {
                    this.isAuthenticated = true;
                    this._triggerEvent('auth_success');
                } else {
                    this.isAuthenticated = false;
                    this._triggerEvent('auth_error', message);
                }
            } else if (message.type === 'alert_notification') {
                this._triggerEvent('alert', message.data);
            } else if (message.type === 'unread_notifications') {
                // Okunmamış bildirimler
                this._triggerEvent('unread_notifications', {
                    count: message.count,
                    notifications: message.notifications
                });

                // Her bir bildirimi ayrı ayrı da tetikle
                if (message.notifications && Array.isArray(message.notifications)) {
                    message.notifications.forEach(notification => {
                        this._triggerEvent('alert', notification);
                    });
                }
            }
        } catch (error) {
            this._debug('Mesaj işleme hatası:', error);
        }
    }

    /**
     * Yeniden bağlanma işlemini başlatır
     *
     * @private
     */
    _reconnect() {
        if (this.reconnectAttempts >= this.options.maxReconnectAttempts) {
            this._debug('Maksimum yeniden bağlanma denemesi aşıldı');
            return;
        }

        this.reconnectAttempts++;
        this._debug(`Yeniden bağlanılıyor (${this.reconnectAttempts}/${this.options.maxReconnectAttempts})...`);

        setTimeout(() => {
            this.connect();
        }, this.options.reconnectInterval);
    }

    /**
     * Belirtilen olay için tüm dinleyicileri tetikler
     *
     * @private
     * @param {string} event - Olay adı
     * @param {*} data - Olay verileri
     */
    _triggerEvent(event, data) {
        if (this.eventListeners[event]) {
            this.eventListeners[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error('Event listener error:', error);
                }
            });
        }
    }

    /**
     * Debug mesajlarını yazdırır
     *
     * @private
     */
    _debug(...args) {
        if (this.options.debug) {
            console.log('[AlertWebsocket]', ...args);
        }
    }
}

// CommonJS ve ES Module desteği
if (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {
    module.exports = AlertWebsocketClient;
} else if (typeof define === 'function' && define.amd) {
    define([], function() {
        return AlertWebsocketClient;
    });
} else if (typeof window !== 'undefined') {
    window.AlertWebsocketClient = AlertWebsocketClient;
}
