-- coindata_all VIEW Definition
-- Bu view sadece isshown=1 olan coinleri içerir (isactive değil!)

CREATE VIEW `coindata_all` AS 
SELECT 
    -- Ana coindata tablosu alanları
    coindata.id,
    coindata.cr_id,
    coindata.symbol,
    coindata.geckoid,
    coindata.name,
    coindata.image,
    coindata.marketcap,
    coindata.marketcap_rank,
    coindata.fdv,
    coindata.total_volume,
    coindata.total_supply,
    coindata.max_supply,
    coindata.circulating_supply,
    coindata.ath,
    coindata.ath_change,
    coindata.atl,
    coindata.atl_change,
    coindata.create_date,
    coindata.update_date,
    coindata.isactive,
    coindata.isshown,
    coindata.total_score,
    coindata.geckoslug,
    coindata.categoryid,
    coindata.sparkline7d,
    coindata.score_change_7d,
    
    -- coindata4 tablosundan
    coindata4.gecko_portfolio_count,
    
    -- coindata3 tablosundan
    coindata3.best_cex_rank,
    coindata3.best_dex_rank,
    coindata3.dex_count,
    coindata3.cex_count,
    
    -- coindata_manual tablosundan
    coindata_manual.redist_mechanism,
    coindata_manual.revenue_share,
    coindata_manual.regulatory_compliance,
    coindata_manual.team_status,
    coindata_manual.use_case,
    coindata_manual.def_inf_token,
    coindata_manual.buyback_mechanism,
    
    -- coin_matcher tablosundan
    coin_matcher.certik_project_id,
    
    -- lunarcrush_social_scores tablosundan
    lunarcrush_social_scores.galaxy_score,
    lunarcrush_social_scores.alt_rank,
    lunarcrush_social_scores.sentiment,
    lunarcrush_social_scores.social_dominance,
    lunarcrush_social_scores.social_volume_24h,
    lunarcrush_social_scores.interactions_24h,
    
    -- cr_vesting tablosundan (subquery ile)
    (SELECT IFNULL(cr_vesting.emission_1y, 0)) AS emission_1y

FROM coindata
    LEFT JOIN coindata3 ON coindata.geckoslug = coindata3.geckoid
    LEFT JOIN coindata4 ON coindata.geckoslug = coindata4.geckoid
    LEFT JOIN coindata_manual ON coindata.geckoid = coindata_manual.geckoid
    LEFT JOIN coin_matcher ON coindata.cr_id = coin_matcher.cr_id
    LEFT JOIN lunarcrush_social_scores ON coin_matcher.lunar_id = lunarcrush_social_scores.lunar_id
    LEFT JOIN cr_vesting ON coindata.geckoslug = cr_vesting.geckoid

-- ÖNEMLİ: Sadece isshown=1 olan coinler dahil ediliyor
-- isactive kontrolü YOK!
WHERE coindata.isshown = 1;

/*
ÖZET:
- Bu view sadece isshown=1 olan coinleri içerir
- isactive bayrağı kontrol edilmiyor
- Dolayısıyla calculate_scores_old fonksiyonu:
  * isshown=1 olan TÜM coinler için çalışır
  * isactive=0 olan coinler de dahil edilir
  * Sadece isshown=0 olan coinler hariç tutulur
*/