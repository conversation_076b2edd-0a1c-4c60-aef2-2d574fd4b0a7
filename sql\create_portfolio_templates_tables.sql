-- Portfolio Templates Tables (Simplified)
-- Minimal structure: only template name and per-coin amounts

CREATE TABLE IF NOT EXISTS portfolio_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    template_name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_template_name (template_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS portfolio_template_assets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    template_id INT NOT NULL,
    coin_id INT NOT NULL,
    amount DECIMAL(36, 18) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_template_id (template_id),
    INDEX idx_coin_id (coin_id),
    UNIQUE KEY uniq_template_coin (template_id, coin_id),
    CONSTRAINT fk_template FOREIGN KEY (template_id) REFERENCES portfolio_templates(id) ON DELETE CASCADE,
    CONSTRAINT fk_template_coin FOREIGN KEY (coin_id) REFERENCES coindata(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
