// API fonksiyonları - profil fotoğrafı işlemleri için

const API_BASE_URL = 'https://your-domain.com'; // Sunucu URL'nizi buraya yazın

/**
 * Profil fotoğrafını yükler
 * @param {File} file - Yüklenecek dosya
 * @returns {Promise<Object>} API yanıtı
 */
export const uploadProfileImage = async (file) => {
  const formData = new FormData();
  formData.append('profile_image', file);
  formData.append('action', 'upload');

  const token = localStorage.getItem('authToken');
  
  const response = await fetch(`${API_BASE_URL}/upload_profile_image.php`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
    },
    body: formData,
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return await response.json();
};

/**
 * Profil fotoğrafını siler
 * @returns {Promise<Object>} API yanıtı
 */
export const deleteProfileImage = async () => {
  const formData = new FormData();
  formData.append('action', 'delete');

  const token = localStorage.getItem('authToken');
  
  const response = await fetch(`${API_BASE_URL}/upload_profile_image.php`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
    },
    body: formData,
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return await response.json();
};

/**
 * Kullanıcının profil fotoğrafını getirir
 * @returns {Promise<Object>} API yanıtı
 */
export const getProfileImage = async () => {
  const token = localStorage.getItem('authToken');
  
  const response = await fetch(`${API_BASE_URL}/client.php`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify({
      f: 'get_profile_image'
    }),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return await response.json();
};

/**
 * Kullanıcı bilgilerini getirir (profil fotoğrafı dahil)
 * @returns {Promise<Object>} API yanıtı
 */
export const getCurrentUser = async () => {
  const token = localStorage.getItem('authToken');
  
  const response = await fetch(`${API_BASE_URL}/client.php`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify({
      f: 'retrieve_current_user'
    }),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return await response.json();
};

// Kullanım örnekleri:

/*
// 1. Profil fotoğrafı yükleme
const handleFileUpload = async (file) => {
  try {
    const result = await uploadProfileImage(file);
    if (result.success) {
      console.log('Yükleme başarılı:', result.data.image_url);
    }
  } catch (error) {
    console.error('Yükleme hatası:', error);
  }
};

// 2. Profil fotoğrafını silme
const handleDeleteImage = async () => {
  try {
    const result = await deleteProfileImage();
    if (result.success) {
      console.log('Silme başarılı');
    }
  } catch (error) {
    console.error('Silme hatası:', error);
  }
};

// 3. Profil fotoğrafını getirme
const fetchProfileImage = async () => {
  try {
    const result = await getProfileImage();
    if (result.success) {
      console.log('Profil fotoğrafı:', result.data.profile_image);
    }
  } catch (error) {
    console.error('Getirme hatası:', error);
  }
};

// 4. Kullanıcı bilgilerini getirme
const fetchUserData = async () => {
  try {
    const result = await getCurrentUser();
    if (result.success) {
      console.log('Kullanıcı bilgileri:', result.data.user);
      console.log('Profil fotoğrafı:', result.data.user.profileImage);
    }
  } catch (error) {
    console.error('Kullanıcı bilgileri hatası:', error);
  }
};
*/
