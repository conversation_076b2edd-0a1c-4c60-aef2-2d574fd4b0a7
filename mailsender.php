<?php
require_once 'vendor/autoload.php';
// cors();

use <PERSON><PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\SMTP;
use P<PERSON><PERSON>ailer\PHPMailer\Exception;
//$payload = file_get_contents('php://input');
//error_log($payload,0);
/*
foreach ($_POST as $param_name => $param_val) {
    echo "Param: ".htmlspecialchars($param_name)."; ";
    echo "Value: ".htmlspecialchars($param_val)."<br />\n";
}
*/


//SADECE TEST AMACLI BU SATIRI ACABİLİRİZ
//send_mail("test body", [ "<EMAIL>"], "Test Subject");






function send_mail($message_body, $mailto, $message_subject)
{
    // Instantiation and passing `true` enables exceptions
    $mail = new PHPMailer(true);
    try {
        foreach ($mailto as $mailaddress) {
            $mail->ClearAllRecipients();
            //Server settings
            $mail->SMTPOptions = array(
                'ssl' => array(
                    'verify_peer' => false,
                    'verify_peer_name' => false,
                    'allow_self_signed' => true
                )
            );
            //  $mail->SMTPDebug = SMTP::DEBUG_SERVER;                 // Enable verbose debug output
            $mail->isSMTP();
            $mail->CharSet = 'UTF-8'; // Send using SMTP
            $mail->Host       = 'smtp.gmail.com';                    // Set the SMTP server to send through
            $mail->SMTPAuth   = true;                                   // Enable SMTP authentication
            $mail->Username   = '<EMAIL>';   //'<EMAIL>';                     // SMTP username
            $mail->Password   =  'kjoi ggck uwnk rftt'; //'hige kelt bfjj yfzn';                               // SMTP password
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;         // Enable TLS encryption; `PHPMailer::ENCRYPTION_SMTPS` encouraged
            $mail->Port       = 587;                                    // TCP port to connect to, use 465 for `PHPMailer::ENCRYPTION_SMTPS` above
            //Recipients
            $mail->setFrom('<EMAIL>', 'Coinscout Support');
            $mail->addAddress($mailaddress);     // Add a recipient
            // Content
            $mail->isHTML(true);                                  // Set email format to HTML
            $mail->Subject = $message_subject;
            $mail->Body    = $message_body;
            $mail->send();
        }
        //    echo 'Message has been sent';
    } catch (Exception $e) {
        echo "Message could not be sent. Mailer Error: {$mail->ErrorInfo}";
    }
    $mail->SmtpClose();
}



// function cors()
// {
//     // Allow from any origin
//     if (isset($_SERVER['HTTP_ORIGIN'])) {
//         header("Access-Control-Allow-Origin: *");
//         header("Access-Control-Allow-Methods: GET, POST, PATCH, PUT, DELETE, OPTIONS");
//         header("Access-Control-Allow-Headers: Origin, Authorization, X-Requested-With, Content-Type, Accept");
//         header('Access-Control-Allow-Credentials: true');
//         header('Access-Control-Max-Age: 86400');    // cache for 1 day
//     }
//     // Access-Control headers are received during OPTIONS requests
//     if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
//         if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_METHOD'])) {
//             // may also be using PUT, PATCH, HEAD etc
//             header("Access-Control-Allow-Methods: GET, POST, PATCH, PUT, DELETE, OPTIONS");
//         }
//         if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS'])) {
//             header("Access-Control-Allow-Headers: Origin, Authorization, X-Requested-With, Content-Type, Accept");
//         }
//         exit(0);
//     }
// }
