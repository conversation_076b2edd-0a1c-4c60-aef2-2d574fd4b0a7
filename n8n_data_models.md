# N8N Portfolio Template Data Models

Bu dokümanda N8N tarafından `n8n_webhook.php` endpoint'ine gönderilmesi gereken veri modelleri açıklanmaktadır.

## 1. create_portfolio_template Action

Portfolio analizi ve skorlama için kullanılır. Veritabanına kayıt yapmaz, sadece analiz sonuçları döner.

### Request Model:
```json
{
  "action": "create_portfolio_template",
  "data": {
    "template_name": "AI Generated Portfolio",
    "assets": [
      {
        "coin_id": 1,
        "allocation": 40.0,
        "symbol": "BTC",
        "name": "Bitcoin",
        "market_cap": 800000000000,
        "ai_score": 85.5,
        "is_vetted": true,
        "categories": ["Store of Value", "Digital Gold"]
      },
      {
        "coin_id": 2,
        "allocation": 30.0,
        "symbol": "ETH",
        "name": "Ethereum",
        "market_cap": 400000000000,
        "ai_score": 88.2,
        "is_vetted": true,
        "categories": ["Smart Contracts", "DeFi"]
      },
      {
        "coin_id": 825,
        "allocation": 20.0,
        "symbol": "ADA",
        "name": "Cardano",
        "market_cap": 15000000000,
        "ai_score": 75.8,
        "is_vetted": false,
        "categories": ["Smart Contracts", "Academic"]
      },
      {
        "coin_id": 1027,
        "allocation": 10.0,
        "symbol": "DOT",
        "name": "Polkadot",
        "market_cap": 8000000000,
        "ai_score": 72.3,
        "is_vetted": false,
        "categories": ["Interoperability", "Parachain"]
      }
    ]
  }
}
```

### Required Fields:
- `template_name`: string - Portfolio template adı
- `assets`: array - Portfolio asset'leri
- `assets[].coin_id`: int - Coin veritabanı ID'si (coindata tablosundan)
- `assets[].allocation`: float - Asset allocation yüzdesi (0-100)

### Optional Fields:
- `assets[].symbol`: string - Coin sembolü (DB'den çekilir)
- `assets[].name`: string - Coin adı (DB'den çekilir)
- `assets[].market_cap`: float - Market cap (DB'den çekilir)
- `assets[].ai_score`: float - AI skoru (DB'den çekilir)
- `assets[].is_vetted`: bool - Vetted durumu (DB'den çekilir)
- `assets[].categories`: array - Kategoriler (DB'den çekilir)

## 2. save_portfolio_template Action

N8N tarafından hazırlanan predefined template'leri veritabanına kaydeder.

### Request Model:
```json
{
  "action": "save_portfolio_template",
  "data": {
    "name": "Conservative Crypto Portfolio",
    "description": "Düşük riskli, büyük piyasa değerli coinlerden oluşan güvenli portföy stratejisi",
    "risk_level": "low",
    "timeframe": "long",
    "strategy": "Büyük piyasa değerli ve stabil coinlere odaklanarak risk minimizasyonu. Bitcoin ve Ethereum ağırlıklı, güvenli altcoinlerle desteklenmiş konservatif yaklaşım.",
    "suitable_for": [
      "Yeni başlayanlar",
      "Risk almak istemeyen yatırımcılar", 
      "Uzun vadeli yatırımcılar",
      "Emeklilik planlaması yapanlar"
    ],
    "not_suitable_for": [
      "Yüksek getiri arayan yatırımcılar",
      "Kısa vadeli spekülatörler",
      "Yüksek risk toleransı olan yatırımcılar"
    ],
    "assets": [
      {
        "coin_id": 1,
        "allocation": 50.0
      },
      {
        "coin_id": 2,
        "allocation": 30.0
      },
      {
        "coin_id": 825,
        "allocation": 15.0
      },
      {
        "coin_id": 52,
        "allocation": 5.0
      }
    ]
  }
}
```

### Required Fields:
- `name`: string - Template adı
- `risk_level`: enum - Risk seviyesi ("low", "medium", "high")
- `timeframe`: enum - Yatırım süresi ("short", "medium", "long")
- `assets`: array - Portfolio asset'leri
- `assets[].coin_id`: int - Coin veritabanı ID'si
- `assets[].allocation`: float - Asset allocation yüzdesi (toplam ~100% olmalı)

### Optional Fields:
- `description`: string - Template açıklaması
- `strategy`: string - Yatırım stratejisi açıklaması
- `suitable_for`: array - Uygun yatırımcı tipleri
- `not_suitable_for`: array - Uygun olmayan yatırımcı tipleri

## Risk Levels
- `low`: Düşük risk, konservatif yaklaşım
- `medium`: Orta risk, dengeli büyüme
- `high`: Yüksek risk, agresif büyüme

## Timeframes
- `short`: Kısa vadeli (3-12 ay)
- `medium`: Orta vadeli (1-3 yıl)
- `long`: Uzun vadeli (3+ yıl)

## Response Format

Her iki action için de standart response formatı:

### Success Response:
```json
{
  "status": "success",
  "message": "Operation completed successfully",
  "template_id": 123,
  "template_name": "Portfolio Name",
  // Additional data based on action
}
```

### Error Response:
```json
{
  "status": "error",
  "message": "Error description",
  "required_fields": {
    // Field requirements based on action
  }
}
```
