-- SQL script to add login tracking functionality
-- This script adds last_login_at to users table and creates user_login_history table
-- Add last_login_at column to users table
ALTER TABLE users
ADD COLUMN last_login_at TIMESTAMP NULL AFTER created_at;

-- Create user_login_history table for detailed login tracking
CREATE TABLE
    IF NOT EXISTS user_login_history (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NULL COMMENT 'User ID - NULL for failed attempts where user was not found',
        login_type ENUM (
            'email',
            'google',
            'apple',
            'twitter',
            'refresh_token'
        ) NOT NULL,
        ip_address VARCHAR(45) NULL,
        user_agent TEXT NULL,
        country VARCHAR(100) NULL,
        city VARCHAR(100) NULL,
        device_type VARCHAR(50) NULL,
        browser VARCHAR(100) NULL,
        os VARCHAR(100) NULL,
        success TINYINT (1) NOT NULL DEFAULT 1,
        failure_reason VARCHAR(255) NULL,
        attempted_email VARCHAR(255) NULL COMMENT 'Email used in failed login attempts (for security analysis)',
        session_duration INT NULL COMMENT 'Session duration in seconds (filled when session ends)',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX (user_id),
        INDEX (login_type),
        INDEX (ip_address),
        INDEX (created_at),
        INDEX (success),
        INDEX (attempted_email),
        INDEX ip_success_date (ip_address, success, created_at)
    ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

-- Add comments to explain the table structure
-- id: Primary key
-- user_id: The ID of the user who logged in (NULL for failed attempts where user was not found)
-- login_type: How the user logged in (email/password, OAuth providers, refresh token)
-- ip_address: The IP address from which the login occurred
-- user_agent: The user agent string from the browser/app
-- country: Country detected from IP (can be filled later with GeoIP)
-- city: City detected from IP (can be filled later with GeoIP)
-- device_type: Type of device (mobile, desktop, tablet)
-- browser: Browser name and version
-- os: Operating system
-- success: Whether the login was successful (1) or failed (0)
-- failure_reason: Reason for login failure (wrong password, account locked, email not found, etc.)
-- attempted_email: Email address used in failed login attempts (for security analysis)
-- session_duration: How long the session lasted (filled when user logs out or session expires)
-- created_at: When the login attempt occurred
-- Create index for performance on common queries
CREATE INDEX idx_user_login_date ON user_login_history (user_id, created_at);

CREATE INDEX idx_login_success_date ON user_login_history (success, created_at);

CREATE INDEX idx_ip_date ON user_login_history (ip_address, created_at);

-- Optional: Create a view for recent successful logins
CREATE VIEW recent_successful_logins AS
SELECT
    ulh.id,
    ulh.user_id,
    u.email,
    u.username,
    ulh.login_type,
    ulh.ip_address,
    ulh.user_agent,
    ulh.country,
    ulh.city,
    ulh.device_type,
    ulh.browser,
    ulh.os,
    ulh.created_at
FROM
    user_login_history ulh
    JOIN users u ON ulh.user_id = u.id
WHERE
    ulh.success = 1
    AND ulh.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY);

-- Optional: Create a view for failed login attempts
CREATE VIEW failed_login_attempts AS
SELECT
    ulh.id,
    ulh.user_id,
    u.email,
    u.username,
    ulh.attempted_email,
    ulh.login_type,
    ulh.ip_address,
    ulh.user_agent,
    ulh.failure_reason,
    ulh.created_at,
    (
        SELECT COUNT(*)
        FROM user_login_history ulh2
        WHERE ulh2.success = 0
          AND ulh2.ip_address = ulh.ip_address
          AND ulh2.created_at BETWEEN ulh.created_at - INTERVAL 1 HOUR AND ulh.created_at
    ) AS attempts_last_hour,
    (
        SELECT COUNT(*)
        FROM user_login_history ulh2
        WHERE ulh2.success = 0
          AND ulh2.user_id = ulh.user_id
          AND ulh.user_id IS NOT NULL
          AND ulh2.created_at BETWEEN ulh.created_at - INTERVAL 1 HOUR AND ulh.created_at
    ) AS user_attempts_last_hour,
    (
        SELECT COUNT(*)
        FROM user_login_history ulh2
        WHERE ulh2.success = 0
          AND ulh2.attempted_email = ulh.attempted_email
          AND ulh.attempted_email IS NOT NULL
          AND ulh2.created_at BETWEEN ulh.created_at - INTERVAL 1 HOUR AND ulh.created_at
    ) AS email_attempts_last_hour
FROM
    user_login_history ulh
    LEFT JOIN users u ON ulh.user_id = u.id
WHERE
    ulh.success = 0
    AND ulh.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY);
