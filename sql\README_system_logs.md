# System Logs Tablosu - StripeLogger Database Integration

## Genel Ba<PERSON>ş

`system_logs` tab<PERSON>u, StripeLogger sınıfının veritabanı kayıt özelliği için tasarlanmıştır. Bu tablo, Stripe event logları da dahil olmak üzere tüm sistem loglarını saklar.

## Tablo <PERSON>ı

```sql
CREATE TABLE `system_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `log_level` varchar(20) NOT NULL,
  `message` text NOT NULL,
  `context` longtext DEFAULT NULL,
  `log_type` varchar(50) NOT NULL DEFAULT 'general',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_log_level` (`log_level`),
  KEY `idx_log_type` (`log_type`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_level_type_date` (`log_level`, `log_type`, `created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## Sütun Açıklamaları

- **id**: Otomatik artan birincil anahtar
- **log_level**: Log seviyesi (debug, info, notice, warning, error, critical, alert, emergency)
- **message**: Log mesajı
- **context**: JSON formatında ek veriler (context, event_type, event_id vb.)
- **log_type**: Log türü (general, stripe_event, authentication, payment vb.)
- **created_at**: Log kaydının oluşturulma zamanı

## StripeLogger Yapılandırması

### Veritabanı Kayıt Seviyeleri

```php
private static array $dbRecordLevels = [
    StripeLogLevel::DEBUG     => false,  // Veritabanına kaydedilmez
    StripeLogLevel::INFO      => true,   // Veritabanına kaydedilir
    StripeLogLevel::NOTICE    => true,   // Veritabanına kaydedilir
    StripeLogLevel::WARNING   => true,   // Veritabanına kaydedilir
    StripeLogLevel::ERROR     => true,   // Veritabanına kaydedilir
    StripeLogLevel::CRITICAL  => true,   // Veritabanına kaydedilir
    StripeLogLevel::ALERT     => true,   // Veritabanına kaydedilir
    StripeLogLevel::EMERGENCY => true,   // Veritabanına kaydedilir
];
```

## Kullanım Örnekleri

### Genel Log Kaydı
```php
StripeLogger::log(StripeLogLevel::INFO, "User login successful", [
    'user_id' => 123,
    'ip_address' => '***********'
]);
```

### Stripe Event Log Kaydı
```php
StripeLogger::logStripeEvent(
    'payment_intent.succeeded',
    'pi_1234567890',
    'Payment completed successfully',
    [
        'amount' => 2000,
        'currency' => 'usd',
        'customer_id' => 'cus_123456'
    ]
);
```

## Log Türleri

- **general**: Genel sistem logları
- **stripe_event**: Stripe webhook event logları
- **authentication**: Kimlik doğrulama logları
- **payment**: Ödeme işlem logları
- **error**: Hata logları
- **security**: Güvenlik logları

## Performans Optimizasyonları

1. **İndeksler**: Log seviyesi, log türü ve tarih için optimize edilmiş indeksler
2. **Sessiz Hata Yönetimi**: Veritabanı hatası durumunda ana uygulama etkilenmez
3. **JSON Context**: Esnek veri saklama için JSON formatı kullanılır

## Bakım ve Temizlik

Büyük veri hacimlerinde performansı korumak için periyodik temizlik önerilir:

```sql
-- 30 günden eski logları sil
DELETE FROM system_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- Sadece DEBUG seviyesindeki logları sil
DELETE FROM system_logs WHERE log_level = 'debug';
```

## Test

Test dosyasını çalıştırarak sistemi test edebilirsiniz:
```bash
php test_stripe_logger.php
```

Bu test dosyası farklı log seviyelerinde kayıtlar oluşturur ve veritabanı entegrasyonunu doğrular.
