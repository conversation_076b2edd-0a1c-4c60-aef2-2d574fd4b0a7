<?php
/**
 * Stripe Subscription Functions
 *
 * This file contains functions related to Stripe subscriptions.
 */

// Include required files
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../secrets.php';
require_once __DIR__ . '/../utils.php';
require_once __DIR__ . '/../models/ResultModel.php';
require_once __DIR__ . '/StripeLogger.php';
require_once __DIR__ . '/email_notifications.php';

// Set Stripe API key
\Stripe\Stripe::setApiKey($stripeSecretKey);

/**
 * Get subscription status for a user
 */
function getSubscriptionStatus() {
    global $link, $data;

    if (!$data || !isset($data['userId'])) {
        echo json_encode(new ErrorResult("Invalid request data or missing userId", 400));
        return;
    }

    $userId = $data['userId'];

    try {
        // Get user's active subscriptions
        $query = "SELECT * FROM stripe_user_subscriptions
                  WHERE user_id = ? AND status IN ('active', 'trialing')
                  ORDER BY created_at DESC LIMIT 1";
        $stmt = mysqli_prepare($link, $query);

        if (!$stmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($stmt, "i", $userId);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $subscription = mysqli_fetch_assoc($result);

        if ($subscription) {
            // Get subscription details from Stripe
            try {
                $stripeSubscription = \Stripe\Subscription::retrieve($subscription['stripe_subscription_id']);

                // Return subscription details
                echo json_encode(new SuccessResult([
                    'active' => true,
                    'subscription' => [
                        'id' => $subscription['id'],
                        'stripe_subscription_id' => $subscription['stripe_subscription_id'],
                        'plan_name' => $subscription['plan_name'],
                        'status' => $subscription['status'],
                        'current_period_end' => isset($stripeSubscription->current_period_end)
                            ? date('Y-m-d H:i:s', $stripeSubscription->current_period_end)
                            : null,
                        'cancel_at_period_end' => isset($stripeSubscription->cancel_at_period_end)
                            ? $stripeSubscription->cancel_at_period_end
                            : false
                    ]
                ]));
            } catch (\Exception $e) {
                // If Stripe API call fails, return data from database
                echo json_encode(new SuccessResult([
                    'active' => true,
                    'subscription' => [
                        'id' => $subscription['id'],
                        'stripe_subscription_id' => $subscription['stripe_subscription_id'],
                        'plan_name' => $subscription['plan_name'],
                        'status' => $subscription['status']
                    ]
                ]));
            }
        } else {
            // No active subscription
            echo json_encode(new SuccessResult([
                'active' => false
            ]));
        }
    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "STRIPE SUBSCRIPTION STATUS ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'userId' => $userId ?? 'unknown'
        ]);
        echo json_encode(new ErrorResult($e->getMessage(), 500));
    }
}

/**
 * Get detailed subscription information for a user
 */
function getUserSubscription() {
    global $link, $data;

    // Get user ID from JWT token
    $userId = authenticate_user_or_error();
    if ($userId === false) {
        return; // Error response already sent
    }

    try {
        // Get user's stripe customer ID
        $userQuery = "SELECT stripe_customer_id FROM users WHERE id = ?";
        $userStmt = mysqli_prepare($link, $userQuery);

        if (!$userStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($userStmt, "i", $userId);
        mysqli_stmt_execute($userStmt);
        $userResult = mysqli_stmt_get_result($userStmt);
        $user = mysqli_fetch_assoc($userResult);

        if (!$user || !$user['stripe_customer_id']) {
            echo json_encode(new SuccessResult([
                'active' => false,
                'message' => 'User has no Stripe customer ID'
            ]));
            return;
        }

        $stripeCustomerId = $user['stripe_customer_id'];

        // Get user's subscriptions
        $query = "SELECT * FROM stripe_user_subscriptions
                  WHERE user_id = ?
                  ORDER BY created_at DESC";
        $stmt = mysqli_prepare($link, $query);

        if (!$stmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($stmt, "i", $userId);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);

        $subscriptions = [];
        $hasActiveSubscription = false;

        while ($subscription = mysqli_fetch_assoc($result)) {
            // Get subscription details from Stripe for each subscription
            try {
                $stripeSubscription = \Stripe\Subscription::retrieve($subscription['stripe_subscription_id']);

                // Check if this is an active subscription
                if (in_array($subscription['status'], ['active', 'trialing'])) {
                    $hasActiveSubscription = true;
                }

                // Get currency from Stripe subscription
                $currency = 'usd'; // Default currency
                $productId = null;
                $planNickname = null;

                // Get plan information from Stripe
                if (isset($stripeSubscription->plan)) {
                    $currency = $stripeSubscription->plan->currency ?? $currency;
                    $productId = $stripeSubscription->plan->product ?? null;
                    $planNickname = $stripeSubscription->plan->nickname ?? null;
                } elseif (isset($stripeSubscription->items) &&
                         isset($stripeSubscription->items->data) &&
                         count($stripeSubscription->items->data) > 0 &&
                         isset($stripeSubscription->items->data[0]->plan)) {
                    $currency = $stripeSubscription->items->data[0]->plan->currency ?? $currency;
                    $productId = $stripeSubscription->items->data[0]->plan->product ?? null;
                    $planNickname = $stripeSubscription->items->data[0]->plan->nickname ?? null;
                }

                // Get plan name - first try nickname from Stripe, then database value, then try to get product name
                $planName = $planNickname;
                if (empty($planName)) {
                    $planName = $subscription['plan_name'];
                }

                // If plan name is "Unknown Plan" or empty, try to get product name from database
                if (empty($planName) || $planName === 'Unknown Plan' || $planName === 'Subscription Plan') {
                    if ($productId) {
                        // Try to get product name from database
                        $productQuery = "SELECT name FROM stripe_products WHERE stripe_product_id = ?";
                        $productStmt = mysqli_prepare($link, $productQuery);

                        if ($productStmt) {
                            mysqli_stmt_bind_param($productStmt, "s", $productId);
                            mysqli_stmt_execute($productStmt);
                            $productResult = mysqli_stmt_get_result($productStmt);
                            $productData = mysqli_fetch_assoc($productResult);

                            if ($productData && isset($productData['name'])) {
                                $planName = $productData['name'];
                                StripeLogger::log(StripeLogLevel::DEBUG, "STRIPE PLAN NAME FROM DATABASE IN GET USER SUBSCRIPTION - Product ID: $productId, Name: $planName");
                            }
                        }

                        // If still no name, try to get from Stripe API
                        if (empty($planName) || $planName === 'Unknown Plan' || $planName === 'Subscription Plan') {
                            try {
                                $product = \Stripe\Product::retrieve($productId);
                                if ($product && isset($product->name)) {
                                    $planName = $product->name;
                                    StripeLogger::log(StripeLogLevel::DEBUG, "STRIPE PLAN NAME FROM API IN GET USER SUBSCRIPTION - Product ID: $productId, Name: $planName");
                                }
                            } catch (\Exception $e) {
                                StripeLogger::log(StripeLogLevel::ERROR, "STRIPE PRODUCT RETRIEVAL ERROR IN GET USER SUBSCRIPTION: " . $e->getMessage(), [
                                    'exception' => get_class($e),
                                    'productId' => $productId ?? 'unknown'
                                ]);
                            }
                        }
                    }
                }

                // Format plan amount correctly (divide by 100 to convert from cents to dollars/TL)
                $planAmount = $subscription['plan_amount'] / 100;

                // Format subscription data
                $subscriptions[] = [
                    'id' => $subscription['id'],
                    'stripe_subscription_id' => $subscription['stripe_subscription_id'],
                    'plan_name' => $planName, // Use the updated plan name
                    'plan_amount' => $planAmount,
                    'plan_amount_raw' => $subscription['plan_amount'],
                    'currency' => $currency,
                    'plan_interval' => $subscription['plan_interval'],
                    'status' => $subscription['status'],
                    'created_at' => $subscription['created_at'],
                    'current_period_start' => isset($stripeSubscription->current_period_start)
                        ? date('Y-m-d H:i:s', $stripeSubscription->current_period_start)
                        : null,
                    'current_period_end' => isset($stripeSubscription->current_period_end)
                        ? date('Y-m-d H:i:s', $stripeSubscription->current_period_end)
                        : null,
                    'cancel_at_period_end' => isset($stripeSubscription->cancel_at_period_end)
                        ? $stripeSubscription->cancel_at_period_end
                        : false,
                    'canceled_at' => isset($stripeSubscription->canceled_at)
                        ? date('Y-m-d H:i:s', $stripeSubscription->canceled_at)
                        : null,
                    'stripe_data' => [
                        'status' => $stripeSubscription->status,
                        'collection_method' => $stripeSubscription->collection_method,
                        'current_period_start' => $stripeSubscription->current_period_start,
                        'current_period_end' => $stripeSubscription->current_period_end,
                        'cancel_at_period_end' => $stripeSubscription->cancel_at_period_end,
                    ]
                ];
            } catch (\Exception $e) {
                // If Stripe API call fails, use data from database
                StripeLogger::log(StripeLogLevel::WARNING, "Error retrieving Stripe subscription: " . $e->getMessage(), [
                    'exception' => get_class($e),
                    'subscriptionId' => $subscription['stripe_subscription_id'] ?? 'unknown'
                ]);

                // Check if this is an active subscription
                if (in_array($subscription['status'], ['active', 'trialing'])) {
                    $hasActiveSubscription = true;
                }

                // Format plan amount correctly (divide by 100 to convert from cents to dollars/TL)
                $planAmount = $subscription['plan_amount'] / 100;

                // Get plan name from database
                $planName = $subscription['plan_name'];

                // If plan name is "Unknown Plan" or empty, try to get product name from database
                if (empty($planName) || $planName === 'Unknown Plan' || $planName === 'Subscription Plan') {
                    // Try to get product ID from database
                    $planId = $subscription['plan_id'];
                    if ($planId) {
                        $priceQuery = "SELECT stripe_product_id FROM stripe_prices WHERE stripe_price_id = ?";
                        $priceStmt = mysqli_prepare($link, $priceQuery);

                        if ($priceStmt) {
                            mysqli_stmt_bind_param($priceStmt, "s", $planId);
                            mysqli_stmt_execute($priceStmt);
                            $priceResult = mysqli_stmt_get_result($priceStmt);
                            $priceData = mysqli_fetch_assoc($priceResult);

                            if ($priceData && isset($priceData['stripe_product_id'])) {
                                $productId = $priceData['stripe_product_id'];

                                // Try to get product name from database
                                $productQuery = "SELECT name FROM stripe_products WHERE stripe_product_id = ?";
                                $productStmt = mysqli_prepare($link, $productQuery);

                                if ($productStmt) {
                                    mysqli_stmt_bind_param($productStmt, "s", $productId);
                                    mysqli_stmt_execute($productStmt);
                                    $productResult = mysqli_stmt_get_result($productStmt);
                                    $productData = mysqli_fetch_assoc($productResult);

                                    if ($productData && isset($productData['name'])) {
                                        $planName = $productData['name'];
                                        StripeLogger::log(StripeLogLevel::DEBUG, "STRIPE PLAN NAME FROM DATABASE IN GET USER SUBSCRIPTION FALLBACK - Product ID: $productId, Name: $planName");
                                    }
                                }
                            }
                        }
                    }
                }

                // Format subscription data without Stripe details
                $subscriptions[] = [
                    'id' => $subscription['id'],
                    'stripe_subscription_id' => $subscription['stripe_subscription_id'],
                    'plan_name' => $planName,
                    'plan_amount' => $planAmount,
                    'plan_amount_raw' => $subscription['plan_amount'],
                    'currency' => 'usd', // Default currency if we can't get it from Stripe
                    'plan_interval' => $subscription['plan_interval'],
                    'status' => $subscription['status'],
                    'created_at' => $subscription['created_at'],
                    'current_period_end' => $subscription['current_period_end'],
                    'cancel_at_period_end' => $subscription['cancel_at_period_end'] ? true : false,
                    'canceled_at' => $subscription['canceled_at'],
                    'error' => 'Could not retrieve live data from Stripe'
                ];
            }
        }

        // Return subscription details
        echo json_encode(new SuccessResult([
            'active' => $hasActiveSubscription,
            'stripe_customer_id' => $stripeCustomerId,
            'subscriptions' => $subscriptions
        ]));
    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "GET USER SUBSCRIPTION ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'userId' => $userId ?? 'unknown'
        ]);
        echo json_encode(new ErrorResult($e->getMessage(), 500));
    }
}

/**
 * Reactivate a subscription that was set to cancel at period end
 */
function reactivateSubscription() {
    global $link, $data;

    // Get user ID from JWT token
    $userId = authenticate_user_or_error();
    if ($userId === false) {
        return; // Error response already sent
    }

    if (!$data || !isset($data['subscriptionId'])) {
        echo json_encode(new ErrorResult("Invalid request data or missing subscriptionId", 400));
        return;
    }

    $subscriptionId = $data['subscriptionId'];

    try {
        // Verify that the subscription belongs to the user
        $query = "SELECT * FROM stripe_user_subscriptions WHERE stripe_subscription_id = ? AND user_id = ?";
        $stmt = mysqli_prepare($link, $query);

        if (!$stmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($stmt, "si", $subscriptionId, $userId);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $subscription = mysqli_fetch_assoc($result);

        if (!$subscription) {
            throw new Exception("Subscription not found or does not belong to the user");
        }

        // Check if the subscription is set to cancel at period end
        if (!$subscription['cancel_at_period_end']) {
            throw new Exception("Subscription is not set to cancel at period end");
        }

        // Reactivate the subscription in Stripe
        $stripeSubscription = \Stripe\Subscription::update($subscriptionId, [
            'cancel_at_period_end' => false
        ]);

        // Update subscription in database
        $updateQuery = "UPDATE stripe_user_subscriptions SET
                        cancel_at_period_end = 0,
                        updated_at = NOW()
                        WHERE stripe_subscription_id = ?";
        $updateStmt = mysqli_prepare($link, $updateQuery);
        mysqli_stmt_bind_param($updateStmt, "s", $subscriptionId);
        mysqli_stmt_execute($updateStmt);

        // Log the reactivation
        StripeLogger::log(StripeLogLevel::INFO, "STRIPE SUBSCRIPTION REACTIVATED - User ID: $userId, Subscription ID: $subscriptionId");

        // Return success response
        echo json_encode(new SuccessResult([
            'reactivated' => true,
            'subscription' => [
                'id' => $subscription['id'],
                'stripe_subscription_id' => $subscription['stripe_subscription_id'],
                'status' => $subscription['status'],
                'cancel_at_period_end' => false
            ]
        ]));

    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "STRIPE SUBSCRIPTION REACTIVATION ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'userId' => $userId ?? 'unknown',
            'subscriptionId' => $subscriptionId ?? 'unknown'
        ]);
        echo json_encode(new ErrorResult($e->getMessage(), 500));
    }
}

/**
 * Cancel a subscription
 */
function cancelSubscription() {
    global $link, $data;

    // Get user ID from JWT token
    $userId = authenticate_user_or_error();
    if ($userId === false) {
        return; // Error response already sent
    }

    if (!$data || !isset($data['subscriptionId'])) {
        echo json_encode(new ErrorResult("Invalid request data or missing subscriptionId", 400));
        return;
    }

    $subscriptionId = $data['subscriptionId'];
    $cancelAtPeriodEnd = isset($data['cancelAtPeriodEnd']) ? $data['cancelAtPeriodEnd'] : true;

    try {
        // Verify that the subscription belongs to the user
        $query = "SELECT * FROM stripe_user_subscriptions WHERE stripe_subscription_id = ? AND user_id = ?";
        $stmt = mysqli_prepare($link, $query);

        if (!$stmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($stmt, "si", $subscriptionId, $userId);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $subscription = mysqli_fetch_assoc($result);

        if (!$subscription) {
            throw new Exception("Subscription not found or does not belong to the user");
        }

        // Cancel the subscription in Stripe
        $stripeSubscription = \Stripe\Subscription::update($subscriptionId, [
            'cancel_at_period_end' => $cancelAtPeriodEnd
        ]);

        if (!$cancelAtPeriodEnd) {
            // Immediately cancel the subscription
            $stripeSubscription = \Stripe\Subscription::update($subscriptionId, [
                'cancel_at_period_end' => false
            ]);
            $stripeSubscription->cancel();

            // Update subscription status in database
            $updateQuery = "UPDATE stripe_user_subscriptions SET
                            status = 'canceled',
                            canceled_at = NOW(),
                            updated_at = NOW()
                            WHERE stripe_subscription_id = ?";
            $updateStmt = mysqli_prepare($link, $updateQuery);
            mysqli_stmt_bind_param($updateStmt, "s", $subscriptionId);
            mysqli_stmt_execute($updateStmt);

            // Update user subscription level if no other active subscriptions
            $checkQuery = "SELECT COUNT(*) as active_count FROM stripe_user_subscriptions
                          WHERE user_id = ? AND status = 'active' AND stripe_subscription_id != ?";
            $checkStmt = mysqli_prepare($link, $checkQuery);
            mysqli_stmt_bind_param($checkStmt, "is", $userId, $subscriptionId);
            mysqli_stmt_execute($checkStmt);
            $checkResult = mysqli_stmt_get_result($checkStmt);
            $row = mysqli_fetch_assoc($checkResult);

            if ($row['active_count'] == 0) {
                $updateUserQuery = "UPDATE users SET subscription_level = 'free' WHERE id = ?";
                $updateUserStmt = mysqli_prepare($link, $updateUserQuery);
                mysqli_stmt_bind_param($updateUserStmt, "i", $userId);

                if (mysqli_stmt_execute($updateUserStmt)) {
                    // Get user email and plan name for cancellation email
                    $userQuery = "SELECT u.email, sus.plan_name
                                 FROM users u
                                 LEFT JOIN stripe_user_subscriptions sus ON sus.stripe_subscription_id = ?
                                 WHERE u.id = ?";
                    $userStmt = mysqli_prepare($link, $userQuery);

                    if ($userStmt) {
                        mysqli_stmt_bind_param($userStmt, "si", $subscriptionId, $userId);
                        mysqli_stmt_execute($userStmt);
                        $userResult = mysqli_stmt_get_result($userStmt);
                        $userData = mysqli_fetch_assoc($userResult);

                        if ($userData && isset($userData['email'])) {
                            // Send cancellation email
                            $endDate = date('Y-m-d');

                            sendSubscriptionCancellationEmail(
                                $userId,
                                $userData['email'],
                                $userData['plan_name'] ?? 'Premium Plan',
                                $endDate,
                                true // Immediate cancellation
                            );
                        }
                    }
                }
            }
        } else {
            // Update subscription in database to reflect cancel_at_period_end
            $updateQuery = "UPDATE stripe_user_subscriptions SET
                            cancel_at_period_end = 1,
                            updated_at = NOW()
                            WHERE stripe_subscription_id = ?";
            $updateStmt = mysqli_prepare($link, $updateQuery);
            mysqli_stmt_bind_param($updateStmt, "s", $subscriptionId);

            if (mysqli_stmt_execute($updateStmt)) {
                // Get user email and subscription details for cancellation email
                $userQuery = "SELECT u.email, sus.plan_name, sus.current_period_end
                             FROM users u
                             JOIN stripe_user_subscriptions sus ON sus.user_id = u.id
                             WHERE sus.stripe_subscription_id = ?";
                $userStmt = mysqli_prepare($link, $userQuery);

                if ($userStmt) {
                    mysqli_stmt_bind_param($userStmt, "s", $subscriptionId);
                    mysqli_stmt_execute($userStmt);
                    $userResult = mysqli_stmt_get_result($userStmt);
                    $userData = mysqli_fetch_assoc($userResult);

                    if ($userData && isset($userData['email'])) {
                        // Send cancellation email
                        $endDate = isset($userData['current_period_end']) ?
                            date('Y-m-d', strtotime($userData['current_period_end'])) :
                            date('Y-m-d', strtotime('+30 days'));

                        sendSubscriptionCancellationEmail(
                            $userId,
                            $userData['email'],
                            $userData['plan_name'] ?? 'Premium Plan',
                            $endDate,
                            false // Not immediate cancellation
                        );
                    }
                }
            }
        }

        // Return success response
        echo json_encode(new SuccessResult([
            'canceled' => true,
            'cancelAtPeriodEnd' => $cancelAtPeriodEnd,
            'subscription' => [
                'id' => $subscription['id'],
                'stripe_subscription_id' => $subscription['stripe_subscription_id'],
                'status' => $cancelAtPeriodEnd ? $subscription['status'] : 'canceled'
            ]
        ]));

    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "STRIPE SUBSCRIPTION CANCELLATION ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'userId' => $userId ?? 'unknown',
            'subscriptionId' => $subscriptionId ?? 'unknown'
        ]);
        echo json_encode(new ErrorResult($e->getMessage(), 500));
    }
}

/**
 * Handle subscription creation event
 *
 * @param object $subscription The subscription object from Stripe
 */
function handleSubscriptionCreated($subscription) {
    global $link;

    try {
        $stripeCustomerId = $subscription->customer;
        $userId = null;

        // 1. First check metadata for user ID
        if (isset($subscription->metadata) && isset($subscription->metadata->user_id)) {
            $metadataUserId = $subscription->metadata->user_id;

            // Find user by metadata user_id
            $query = "SELECT id FROM users WHERE id = ?";
            $stmt = mysqli_prepare($link, $query);

            if (!$stmt) {
                throw new Exception("Database error: " . mysqli_error($link));
            }

            mysqli_stmt_bind_param($stmt, "i", $metadataUserId);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $user = mysqli_fetch_assoc($result);

            if ($user) {
                $userId = $user['id'];
                StripeLogger::log(StripeLogLevel::DEBUG, "STRIPE USER FOUND BY METADATA - User ID: $userId");

                // Update user's stripe_customer_id
                $updateQuery = "UPDATE users SET stripe_customer_id = ? WHERE id = ?";
                $updateStmt = mysqli_prepare($link, $updateQuery);
                mysqli_stmt_bind_param($updateStmt, "si", $stripeCustomerId, $userId);
                mysqli_stmt_execute($updateStmt);
            }
        }

        // 2. If no user ID in metadata or not found, try to find by stripe_customer_id
        if (!$userId) {
            $query = "SELECT id FROM users WHERE stripe_customer_id = ?";
            $stmt = mysqli_prepare($link, $query);

            if (!$stmt) {
                throw new Exception("Database error: " . mysqli_error($link));
            }

            mysqli_stmt_bind_param($stmt, "s", $stripeCustomerId);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $user = mysqli_fetch_assoc($result);

            if ($user) {
                $userId = $user['id'];
                StripeLogger::log(StripeLogLevel::DEBUG, "STRIPE USER FOUND BY CUSTOMER ID - User ID: $userId");
            }
        }

        // 3. If still no user found, get customer information and create new user
        if (!$userId) {
            // Get customer information
            $customer = \Stripe\Customer::retrieve($stripeCustomerId);
            $customerEmail = $customer->email;

            StripeLogger::log(StripeLogLevel::DEBUG, "STRIPE SUBSCRIPTION DATA: " . json_encode($subscription));
            StripeLogger::log(StripeLogLevel::DEBUG, "STRIPE CUSTOMER DATA: " . json_encode($customer));

            // Create new user
            handleNewCustomer($customer);

            // Get newly created user
            $query = "SELECT id FROM users WHERE stripe_customer_id = ?";
            $stmt = mysqli_prepare($link, $query);

            if (!$stmt) {
                throw new Exception("Database error: " . mysqli_error($link));
            }

            mysqli_stmt_bind_param($stmt, "s", $stripeCustomerId);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $user = mysqli_fetch_assoc($result);

            if ($user) {
                $userId = $user['id'];
                StripeLogger::log(StripeLogLevel::INFO, "STRIPE NEW USER CREATED - User ID: $userId");
            } else {
                throw new Exception("Failed to create or find user for subscription");
            }
        }

        // Get plan information
        $planId = $subscription->plan->id;
        $planAmount = $subscription->plan->amount;
        $planInterval = $subscription->plan->interval;
        $stripePriceId = $subscription->plan->id; // Price ID is the same as plan ID in Stripe
        $customerEmail = isset($customer) ? $customer->email : '';

        // Get plan name - first try nickname, then try to get product name
        $planName = $subscription->plan->nickname ?? null;

        // If nickname is not available, try to get product name from database
        if (empty($planName) && isset($subscription->plan->product)) {
            $productId = $subscription->plan->product;

            // Try to get product name from database
            $productQuery = "SELECT name FROM stripe_products WHERE stripe_product_id = ?";
            $productStmt = mysqli_prepare($link, $productQuery);

            if ($productStmt) {
                mysqli_stmt_bind_param($productStmt, "s", $productId);
                mysqli_stmt_execute($productStmt);
                $productResult = mysqli_stmt_get_result($productStmt);
                $productData = mysqli_fetch_assoc($productResult);

                if ($productData && isset($productData['name'])) {
                    $planName = $productData['name'];
                    StripeLogger::log(StripeLogLevel::DEBUG, "STRIPE PLAN NAME FROM DATABASE - Product ID: $productId, Name: $planName");
                }
            }

            // If still no name, try to get from Stripe API
            if (empty($planName)) {
                try {
                    $product = \Stripe\Product::retrieve($productId);
                    if ($product && isset($product->name)) {
                        $planName = $product->name;
                        StripeLogger::log(StripeLogLevel::DEBUG, "STRIPE PLAN NAME FROM API - Product ID: $productId, Name: $planName");
                    }
                } catch (\Exception $e) {
                    StripeLogger::log(StripeLogLevel::ERROR, "STRIPE PRODUCT RETRIEVAL ERROR: " . $e->getMessage(), [
                        'exception' => get_class($e),
                        'productId' => $productId ?? 'unknown'
                    ]);
                }
            }
        }

        // If still no name, use a default
        if (empty($planName)) {
            $planName = 'Subscription Plan';
        }

        // If customer information not yet retrieved, get it now
        if (!isset($customer)) {
            $customer = \Stripe\Customer::retrieve($stripeCustomerId);
            $customerEmail = $customer->email;
        }

        // E-posta kontrolü yap
        if (empty($customerEmail)) {
            // Kullanıcının e-posta adresini veritabanından almaya çalış
            $userEmailQuery = "SELECT email FROM users WHERE id = ?";
            $userEmailStmt = mysqli_prepare($link, $userEmailQuery);

            if ($userEmailStmt) {
                mysqli_stmt_bind_param($userEmailStmt, "i", $userId);
                mysqli_stmt_execute($userEmailStmt);
                $userEmailResult = mysqli_stmt_get_result($userEmailStmt);
                $userEmailData = mysqli_fetch_assoc($userEmailResult);

                if ($userEmailData && !empty($userEmailData['email'])) {
                    $customerEmail = $userEmailData['email'];
                    StripeLogger::log(StripeLogLevel::INFO, "Retrieved email from database for user ID: $userId, Email: $customerEmail");
                } else {
                    // Geçici e-posta oluştur
                    $customerEmail = "user_" . $userId . "@placeholder.coinscout.app";
                    StripeLogger::log(StripeLogLevel::WARNING, "Using placeholder email for user ID: $userId, Email: $customerEmail");
                }
            }
        }

        // Abonelik kaydını kontrol et - aynı abonelik ID'si zaten var mı?
        $checkSubscriptionQuery = "SELECT id FROM stripe_user_subscriptions WHERE stripe_subscription_id = ?";
        $checkSubscriptionStmt = mysqli_prepare($link, $checkSubscriptionQuery);

        if ($checkSubscriptionStmt) {
            mysqli_stmt_bind_param($checkSubscriptionStmt, "s", $subscription->id);
            mysqli_stmt_execute($checkSubscriptionStmt);
            $checkSubscriptionResult = mysqli_stmt_get_result($checkSubscriptionStmt);

            if (mysqli_fetch_assoc($checkSubscriptionResult)) {
                StripeLogger::log(StripeLogLevel::WARNING, "Subscription already exists in database: " . $subscription->id);

                // Abonelik zaten var, güncelleme yap
                $updateQuery = "UPDATE stripe_user_subscriptions SET
                    user_id = ?,
                    stripe_customer_id = ?,
                    customer_email = ?,
                    plan_id = ?,
                    plan_name = ?,
                    plan_amount = ?,
                    `plan_interval` = ?,
                    status = ?,
                    stripe_price_id = ?,
                    updated_at = NOW()
                    WHERE stripe_subscription_id = ?";

                $updateStmt = mysqli_prepare($link, $updateQuery);

                if (!$updateStmt) {
                    throw new Exception("Database error: " . mysqli_error($link));
                }

                mysqli_stmt_bind_param(
                    $updateStmt,
                    "issssdsss",
                    $userId,
                    $stripeCustomerId,
                    $customerEmail,
                    $planId,
                    $planName,
                    $planAmount,
                    $planInterval,
                    $subscription->status,
                    $stripePriceId,
                    $subscription->id
                );

                if (!mysqli_stmt_execute($updateStmt)) {
                    throw new Exception("Failed to update subscription: " . mysqli_error($link));
                }

                StripeLogger::log(StripeLogLevel::INFO, "Updated existing subscription: " . $subscription->id);
            } else {
                // Yeni abonelik oluştur
                $insertQuery = "INSERT INTO stripe_user_subscriptions (
                    user_id,
                    stripe_customer_id,
                    stripe_subscription_id,
                    customer_email,
                    plan_id,
                    plan_name,
                    plan_amount,
                    `plan_interval`,
                    status,
                    stripe_price_id,
                    created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

                $insertStmt = mysqli_prepare($link, $insertQuery);

                if (!$insertStmt) {
                    throw new Exception("Database error: " . mysqli_error($link));
                }

                mysqli_stmt_bind_param(
                    $insertStmt,
                    "isssssdsss",
                    $userId,
                    $stripeCustomerId,
                    $subscription->id,
                    $customerEmail,
                    $planId,
                    $planName,
                    $planAmount,
                    $planInterval,
                    $subscription->status,
                    $stripePriceId
                );

                if (!mysqli_stmt_execute($insertStmt)) {
                    throw new Exception("Failed to save subscription: " . mysqli_error($link));
                }

                StripeLogger::log(StripeLogLevel::INFO, "Created new subscription: " . $subscription->id);
            }
        } else {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        // Determine subscription level
        $subscriptionLevel = determineSubscriptionLevel($planName);

        // Update subscription_level field in users table
        $updateUserQuery = "UPDATE users SET subscription_level = ? WHERE id = ?";
        $updateUserStmt = mysqli_prepare($link, $updateUserQuery);

        if (!$updateUserStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($updateUserStmt, "si", $subscriptionLevel, $userId);

        if (!mysqli_stmt_execute($updateUserStmt)) {
            throw new Exception("Failed to update user subscription level: " . mysqli_error($link));
        }

        StripeLogger::log(StripeLogLevel::INFO, "STRIPE SUBSCRIPTION CREATED - User ID: $userId, Level: $subscriptionLevel, Subscription ID: " . $subscription->id);

        // Get user email
        $userQuery = "SELECT email FROM users WHERE id = ?";
        $userStmt = mysqli_prepare($link, $userQuery);

        if ($userStmt) {
            mysqli_stmt_bind_param($userStmt, "i", $userId);
            mysqli_stmt_execute($userStmt);
            $userResult = mysqli_stmt_get_result($userStmt);
            $userData = mysqli_fetch_assoc($userResult);

            if ($userData && isset($userData['email']) && strpos($userData['email'], '@placeholder.coinscout.app') === false) {
                $userEmail = $userData['email'];

                // Format amount for display (convert from cents to dollars/TL)
                $formattedAmount = number_format($planAmount / 100, 2);

                // Get currency from Stripe subscription
                $currency = 'usd'; // Default currency
                if (isset($subscription->plan) && isset($subscription->plan->currency)) {
                    $currency = $subscription->plan->currency;
                }

                // Get next billing date
                $nextBillingDate = date('Y-m-d', $subscription->current_period_end ?? (time() + (30 * 24 * 60 * 60)));

                // Send purchase confirmation email
                sendSubscriptionPurchaseEmail(
                    $userId,
                    $userEmail,
                    $subscription,
                    $planName,
                    $formattedAmount,
                    $currency,
                    $planInterval,
                    $nextBillingDate
                );
            } else {
                StripeLogger::log(StripeLogLevel::INFO, "Skipping email notification - User has placeholder email or no email: $userId");
            }
        }

    } catch (\Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "STRIPE SUBSCRIPTION ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'subscriptionId' => $subscription->id ?? 'unknown',
            'userId' => $userId ?? 'unknown'
        ]);
        throw $e;
    }
}
