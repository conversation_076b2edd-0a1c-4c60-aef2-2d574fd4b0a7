<?php
/**
 * Login Tracking Functions
 * This file contains functions to track user login activities
 */

/**
 * Update user's last login timestamp
 *
 * @param int $userId User ID
 * @return bool Success status
 */
function updateLastLogin($userId) {
    global $link;

    $query = "UPDATE users SET last_login_at = NOW() WHERE id = ?";
    $stmt = mysqli_prepare($link, $query);

    if (!$stmt) {
        error_log("Failed to prepare last login update statement: " . mysqli_error($link));
        return false;
    }

    mysqli_stmt_bind_param($stmt, "i", $userId);
    $result = mysqli_stmt_execute($stmt);

    if (!$result) {
        error_log("Failed to update last login: " . mysqli_error($link));
        return false;
    }

    return true;
}

/**
 * Log login attempt to user_login_history table
 *
 * @param int|null $userId User ID (null for failed attempts without valid user)
 * @param string $loginType Type of login (email, google, apple, twitter, refresh_token)
 * @param bool $success Whether login was successful
 * @param string|null $failureReason Reason for failure if unsuccessful
 * @param array $additionalData Additional data like user agent, device info, attempted_email
 * @return bool Success status
 */
function logLoginAttempt($userId, $loginType, $success = true, $failureReason = null, $additionalData = []) {
    global $link;

    // Include StripeLogger if not already included
    if (!class_exists('StripeLogger')) {
        require_once 'stripe/StripeLogger.php';
    }

    // Get IP address
    $ipAddress = $_SERVER['REMOTE_ADDR'] ?? null;

    // Get user agent
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? null;

    // Parse additional data
    $deviceType = $additionalData['device_type'] ?? null;
    $browser = $additionalData['browser'] ?? null;
    $os = $additionalData['os'] ?? null;
    $country = $additionalData['country'] ?? null;
    $city = $additionalData['city'] ?? null;
    $attemptedEmail = $additionalData['attempted_email'] ?? null;

    // For successful logins, attempted_email is usually not needed, but we can set it to null
    // For failed logins, it should contain the email that was attempted

    $query = "INSERT INTO user_login_history (
        user_id,
        login_type,
        ip_address,
        user_agent,
        country,
        city,
        device_type,
        browser,
        os,
        success,
        failure_reason,
        attempted_email,
        created_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

    $stmt = mysqli_prepare($link, $query);

    if (!$stmt) {
        $error = mysqli_error($link);
        error_log("Failed to prepare login history statement: " . $error);
        StripeLogger::log(StripeLogLevel::ERROR, "LOGIN TRACKING - Database prepare failed", [
            'error' => $error,
            'user_id' => $userId,
            'login_type' => $loginType,
            'success' => $success
        ]);
        return false;
    }

    $successInt = $success ? 1 : 0;

    mysqli_stmt_bind_param($stmt, "issssssssiss",
        $userId,
        $loginType,
        $ipAddress,
        $userAgent,
        $country,
        $city,
        $deviceType,
        $browser,
        $os,
        $successInt,
        $failureReason,
        $attemptedEmail
    );

    $result = mysqli_stmt_execute($stmt);

    if (!$result) {
        $error = mysqli_error($link);
        error_log("Failed to log login attempt: " . $error);
        StripeLogger::log(StripeLogLevel::ERROR, "LOGIN TRACKING - Database execute failed", [
            'error' => $error,
            'user_id' => $userId,
            'login_type' => $loginType,
            'success' => $success,
            'ip_address' => $ipAddress,
            'attempted_email' => $attemptedEmail
        ]);
        return false;
    }

    return true;
}

/**
 * Parse user agent to extract device and browser information
 *
 * @param string $userAgent User agent string
 * @return array Parsed information
 */
function parseUserAgent($userAgent) {
    $info = [
        'device_type' => 'unknown',
        'browser' => 'unknown',
        'os' => 'unknown'
    ];

    if (empty($userAgent)) {
        return $info;
    }

    // Detect device type
    if (preg_match('/Mobile|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i', $userAgent)) {
        if (preg_match('/iPad/i', $userAgent)) {
            $info['device_type'] = 'tablet';
        } else {
            $info['device_type'] = 'mobile';
        }
    } else {
        $info['device_type'] = 'desktop';
    }

    // Detect browser
    if (preg_match('/Chrome\/([0-9.]+)/i', $userAgent, $matches)) {
        $info['browser'] = 'Chrome ' . $matches[1];
    } elseif (preg_match('/Firefox\/([0-9.]+)/i', $userAgent, $matches)) {
        $info['browser'] = 'Firefox ' . $matches[1];
    } elseif (preg_match('/Safari\/([0-9.]+)/i', $userAgent, $matches)) {
        if (preg_match('/Chrome/i', $userAgent)) {
            // Chrome also contains Safari in user agent
        } else {
            $info['browser'] = 'Safari ' . $matches[1];
        }
    } elseif (preg_match('/Edge\/([0-9.]+)/i', $userAgent, $matches)) {
        $info['browser'] = 'Edge ' . $matches[1];
    } elseif (preg_match('/Opera\/([0-9.]+)/i', $userAgent, $matches)) {
        $info['browser'] = 'Opera ' . $matches[1];
    }

    // Detect OS
    if (preg_match('/Windows NT ([0-9.]+)/i', $userAgent, $matches)) {
        $info['os'] = 'Windows ' . $matches[1];
    } elseif (preg_match('/Mac OS X ([0-9_]+)/i', $userAgent, $matches)) {
        $info['os'] = 'macOS ' . str_replace('_', '.', $matches[1]);
    } elseif (preg_match('/Linux/i', $userAgent)) {
        $info['os'] = 'Linux';
    } elseif (preg_match('/Android ([0-9.]+)/i', $userAgent, $matches)) {
        $info['os'] = 'Android ' . $matches[1];
    } elseif (preg_match('/iPhone OS ([0-9_]+)/i', $userAgent, $matches)) {
        $info['os'] = 'iOS ' . str_replace('_', '.', $matches[1]);
    } elseif (preg_match('/iPad.*OS ([0-9_]+)/i', $userAgent, $matches)) {
        $info['os'] = 'iPadOS ' . str_replace('_', '.', $matches[1]);
    }

    return $info;
}

/**
 * Handle successful login - updates last_login_at and logs the attempt
 *
 * @param int $userId User ID
 * @param string $loginType Type of login
 * @param array $additionalData Additional data
 * @return bool Success status
 */
function handleSuccessfulLogin($userId, $loginType, $additionalData = []) {
    // Include StripeLogger if not already included
    if (!class_exists('StripeLogger')) {
        require_once 'stripe/StripeLogger.php';
    }

    // Parse user agent for device/browser info
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $parsedInfo = parseUserAgent($userAgent);

    // Merge parsed info with additional data
    $logData = array_merge($parsedInfo, $additionalData);

    // Update last login timestamp
    $lastLoginUpdated = updateLastLogin($userId);

    // Log the successful login attempt
    $loginLogged = logLoginAttempt($userId, $loginType, true, null, $logData);

    if ($lastLoginUpdated && $loginLogged) {
        StripeLogger::log(StripeLogLevel::DEBUG, "LOGIN TRACKING - Successfully tracked login", [
            'user_id' => $userId,
            'login_type' => $loginType,
            'device_type' => $logData['device_type'],
            'browser' => $logData['browser'],
            'os' => $logData['os']
        ]);
        return true;
    } else {
        StripeLogger::log(StripeLogLevel::WARNING, "LOGIN TRACKING - Partial failure in tracking login", [
            'user_id' => $userId,
            'login_type' => $loginType,
            'last_login_updated' => $lastLoginUpdated,
            'login_logged' => $loginLogged
        ]);
        return false;
    }
}

/**
 * Handle failed login attempt
 *
 * @param int|null $userId User ID (null if user not found)
 * @param string $loginType Type of login
 * @param string $failureReason Reason for failure
 * @param array $additionalData Additional data (should include 'attempted_email' for security analysis)
 * @return bool Success status
 */
function handleFailedLogin($userId, $loginType, $failureReason, $additionalData = []) {
    // Include StripeLogger if not already included
    if (!class_exists('StripeLogger')) {
        require_once 'stripe/StripeLogger.php';
    }

    // Parse user agent for device/browser info
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $parsedInfo = parseUserAgent($userAgent);

    // Merge parsed info with additional data
    $logData = array_merge($parsedInfo, $additionalData);

    // Log the failed login attempt
    $loginLogged = logLoginAttempt($userId, $loginType, false, $failureReason, $logData);

    if ($loginLogged) {
        StripeLogger::log(StripeLogLevel::DEBUG, "LOGIN TRACKING - Successfully tracked failed login", [
            'user_id' => $userId,
            'login_type' => $loginType,
            'failure_reason' => $failureReason,
            'attempted_email' => $logData['attempted_email'] ?? null,
            'device_type' => $logData['device_type'],
            'browser' => $logData['browser'],
            'os' => $logData['os']
        ]);

        // Check for suspicious activity
        checkSuspiciousActivity($logData['attempted_email'] ?? null, $_SERVER['REMOTE_ADDR'] ?? null);

        return true;
    } else {
        StripeLogger::log(StripeLogLevel::WARNING, "LOGIN TRACKING - Failed to track failed login", [
            'user_id' => $userId,
            'login_type' => $loginType,
            'failure_reason' => $failureReason,
            'attempted_email' => $logData['attempted_email'] ?? null
        ]);
        return false;
    }
}

/**
 * Get recent login history for a user
 *
 * @param int $userId User ID
 * @param int $limit Number of records to return
 * @param int $days Number of days to look back
 * @return array Login history
 */
function getUserLoginHistory($userId, $limit = 10, $days = 30) {
    global $link;

    $query = "SELECT
        login_type,
        ip_address,
        user_agent,
        country,
        city,
        device_type,
        browser,
        os,
        success,
        failure_reason,
        created_at
    FROM user_login_history
    WHERE user_id = ?
    AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
    ORDER BY created_at DESC
    LIMIT ?";

    $stmt = mysqli_prepare($link, $query);

    if (!$stmt) {
        error_log("Failed to prepare login history query: " . mysqli_error($link));
        return [];
    }

    mysqli_stmt_bind_param($stmt, "iii", $userId, $days, $limit);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    $history = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $history[] = $row;
    }

    return $history;
}

/**
 * Check for suspicious login activity
 *
 * @param string|null $attemptedEmail Email used in failed login attempt
 * @param string|null $ipAddress IP address of the attempt
 * @return void
 */
function checkSuspiciousActivity($attemptedEmail, $ipAddress) {
    if (!class_exists('StripeLogger')) {
        require_once 'stripe/StripeLogger.php';
    }

    global $link;

    // Check for too many failed attempts from same IP in last hour
    if ($ipAddress) {
        $ipQuery = "SELECT COUNT(*) as attempt_count
                   FROM user_login_history
                   WHERE ip_address = ?
                   AND success = 0
                   AND created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)";

        $stmt = mysqli_prepare($link, $ipQuery);
        if ($stmt) {
            mysqli_stmt_bind_param($stmt, "s", $ipAddress);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $row = mysqli_fetch_assoc($result);

            if ($row['attempt_count'] >= 10) {
                StripeLogger::log(StripeLogLevel::WARNING, "SECURITY ALERT - Suspicious IP activity detected", [
                    'ip_address' => $ipAddress,
                    'failed_attempts_last_hour' => $row['attempt_count'],
                    'attempted_email' => $attemptedEmail
                ]);
            }
        }
    }

    // Check for email enumeration attempts
    if ($attemptedEmail) {
        $emailQuery = "SELECT COUNT(*) as attempt_count
                      FROM user_login_history
                      WHERE attempted_email = ?
                      AND success = 0
                      AND created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)";

        $stmt = mysqli_prepare($link, $emailQuery);
        if ($stmt) {
            mysqli_stmt_bind_param($stmt, "s", $attemptedEmail);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $row = mysqli_fetch_assoc($result);

            if ($row['attempt_count'] >= 5) {
                StripeLogger::log(StripeLogLevel::WARNING, "SECURITY ALERT - Possible email enumeration attack", [
                    'attempted_email' => $attemptedEmail,
                    'failed_attempts_last_hour' => $row['attempt_count'],
                    'ip_address' => $ipAddress
                ]);
            }
        }
    }
}

/**
 * Get failed login statistics for security analysis
 *
 * @param int $hours Number of hours to look back (default: 24)
 * @return array Statistics about failed login attempts
 */
function getFailedLoginStats($hours = 24) {
    global $link;

    $stats = [
        'total_failed_attempts' => 0,
        'unique_ips' => 0,
        'unique_emails' => 0,
        'top_failing_ips' => [],
        'top_failing_emails' => []
    ];

    // Get total failed attempts
    $totalQuery = "SELECT COUNT(*) as total
                   FROM user_login_history
                   WHERE success = 0
                   AND created_at >= DATE_SUB(NOW(), INTERVAL ? HOUR)";

    $stmt = mysqli_prepare($link, $totalQuery);
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, "i", $hours);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $row = mysqli_fetch_assoc($result);
        $stats['total_failed_attempts'] = $row['total'];
    }

    // Get unique IPs and emails
    $uniqueQuery = "SELECT
                        COUNT(DISTINCT ip_address) as unique_ips,
                        COUNT(DISTINCT attempted_email) as unique_emails
                    FROM user_login_history
                    WHERE success = 0
                    AND created_at >= DATE_SUB(NOW(), INTERVAL ? HOUR)";

    $stmt = mysqli_prepare($link, $uniqueQuery);
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, "i", $hours);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $row = mysqli_fetch_assoc($result);
        $stats['unique_ips'] = $row['unique_ips'];
        $stats['unique_emails'] = $row['unique_emails'];
    }

    // Get top failing IPs
    $topIpsQuery = "SELECT ip_address, COUNT(*) as attempt_count
                    FROM user_login_history
                    WHERE success = 0
                    AND created_at >= DATE_SUB(NOW(), INTERVAL ? HOUR)
                    AND ip_address IS NOT NULL
                    GROUP BY ip_address
                    ORDER BY attempt_count DESC
                    LIMIT 10";

    $stmt = mysqli_prepare($link, $topIpsQuery);
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, "i", $hours);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        while ($row = mysqli_fetch_assoc($result)) {
            $stats['top_failing_ips'][] = $row;
        }
    }

    // Get top failing emails
    $topEmailsQuery = "SELECT attempted_email, COUNT(*) as attempt_count
                       FROM user_login_history
                       WHERE success = 0
                       AND created_at >= DATE_SUB(NOW(), INTERVAL ? HOUR)
                       AND attempted_email IS NOT NULL
                       GROUP BY attempted_email
                       ORDER BY attempt_count DESC
                       LIMIT 10";

    $stmt = mysqli_prepare($link, $topEmailsQuery);
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, "i", $hours);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        while ($row = mysqli_fetch_assoc($result)) {
            $stats['top_failing_emails'][] = $row;
        }
    }

    return $stats;
}
?>
