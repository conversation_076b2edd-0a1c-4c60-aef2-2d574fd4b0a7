-- SQL script to create the subscription_features table with multilingual support
-- This table stores feature descriptions for each subscription plan in multiple languages

CREATE TABLE IF NOT EXISTS subscription_features (
    id INT AUTO_INCREMENT PRIMARY KEY,
    subscription_level VARCHAR(50) NOT NULL,
    feature_key VARCHAR(100) NOT NULL,
    display_order INT DEFAULT 0,
    
    -- Arabic
    title_AR VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    description_AR TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    
    -- German
    title_DE VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    description_DE TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    
    -- English
    title_EN VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    description_EN TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    
    -- Spanish
    title_ES VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    description_ES TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    
    -- French
    title_FR VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    description_FR TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    
    -- Hindi
    title_HI VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    description_HI TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    
    -- Indonesian
    title_ID VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    description_ID TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    
    -- Italian
    title_IT VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    description_IT TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    
    -- Japanese
    title_JA VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    description_JA TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    
    -- Korean
    title_KO VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    description_KO TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    
    -- Portuguese
    title_PT VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    description_PT TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    
    -- Russian
    title_RU VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    description_RU TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    
    -- Turkish
    title_TR VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    description_TR TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    
    -- Vietnamese
    title_VI VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    description_VI TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    
    -- Chinese
    title_ZH VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    description_ZH TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX (subscription_level),
    INDEX (feature_key),
    INDEX (subscription_level, display_order),
    UNIQUE KEY unique_level_feature (subscription_level, feature_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add comments to explain the table structure
-- subscription_features:
-- id: Primary key
-- subscription_level: The subscription level (free, basic, advance, premium)
-- feature_key: Unique identifier for the feature (e.g., 'watchlists', 'alerts', 'search')
-- display_order: Order in which features should be displayed (0 = first)
-- title_XX: Feature title for each language (XX = language code in uppercase)
-- description_XX: Feature description for each language (XX = language code in uppercase)
-- created_at: When the record was created
-- updated_at: When the record was last updated

-- Supported languages (from language_config.php):
-- AR (Arabic), DE (German), EN (English), ES (Spanish), FR (French)
-- HI (Hindi), ID (Indonesian), IT (Italian), JA (Japanese), KO (Korean)
-- PT (Portuguese), RU (Russian), TR (Turkish), VI (Vietnamese), ZH (Chinese)
