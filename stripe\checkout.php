<?php
/**
 * Stripe Checkout Functions
 *
 * This file contains functions related to Stripe Checkout sessions.
 */

// Include required files
require_once __DIR__ . '/plan.php';
require_once __DIR__ . '/StripeLogger.php';

// Function determineSubscriptionLevel() is now defined in stripe/plan.php
// Including it here to avoid duplicate function declaration errors

/**
 * Create a new Stripe Checkout session
 */
function createCheckoutSession() {
    global $link, $data;

    if (!$data) {
        StripeLogger::log(StripeLogLevel::ERROR, "STRIPE CHECKOUT ERROR - Invalid request data");
        echo json_encode(new ErrorResult("Invalid request data", 400));
        return;
    }

    try {
        // Validate required parameters
        if (!isset($data['priceId']) || !isset($data['userId'])) {
            throw new Exception("Missing required parameters: priceId and userId are required");
        }

        $priceId = $data['priceId'];
        $userId = $data['userId'];
        // $successUrl = 'https://coinscout.app/payment-success';
        // $cancelUrl = 'https://coinscout.app/payment-cancel';
        //TODO alttaki url tanimlamalari kalkacak statik olacak. front endden gonderilmeyecek!
        $successUrl = isset($data['successUrl']) ? $data['successUrl'] : 'https://coinscout.app/payment-success';
        $cancelUrl = isset($data['cancelUrl']) ? $data['cancelUrl'] : 'https://coinscout.app/payment-cancel';

        // Get user information
        $query = "SELECT email, stripe_customer_id FROM users WHERE id = ?";
        $stmt = mysqli_prepare($link, $query);

        if (!$stmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($stmt, "i", $userId);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $user = mysqli_fetch_assoc($result);

        if (!$user) {
            throw new Exception("User not found");
        }

        $customerEmail = $user['email'];
        $stripeCustomerId = $user['stripe_customer_id'];

        // Check if user already has an active subscription
        $activeSubscriptionQuery = "SELECT sus.id, sus.stripe_subscription_id, sus.plan_id, sus.plan_name, sus.plan_amount, sus.plan_interval
                                   FROM stripe_user_subscriptions sus
                                   WHERE sus.user_id = ? AND sus.status IN ('active', 'trialing')
                                   LIMIT 1";
        $activeSubStmt = mysqli_prepare($link, $activeSubscriptionQuery);

        if (!$activeSubStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($activeSubStmt, "i", $userId);
        mysqli_stmt_execute($activeSubStmt);
        $activeSubResult = mysqli_stmt_get_result($activeSubStmt);
        $activeSubscription = mysqli_fetch_assoc($activeSubResult);

        // If user already has an active subscription, suggest upgrade instead
        if ($activeSubscription) {
            // Get current product ID from the active subscription plan ID
            $currentProductId = null;
            $currentProductQuery = "SELECT stripe_product_id FROM stripe_prices WHERE stripe_price_id = ?";
            $currentProductStmt = mysqli_prepare($link, $currentProductQuery);

            if ($currentProductStmt) {
                mysqli_stmt_bind_param($currentProductStmt, "s", $activeSubscription['plan_id']);
                mysqli_stmt_execute($currentProductStmt);
                $currentProductResult = mysqli_stmt_get_result($currentProductStmt);
                $currentProductData = mysqli_fetch_assoc($currentProductResult);

                if ($currentProductData && isset($currentProductData['stripe_product_id'])) {
                    $currentProductId = $currentProductData['stripe_product_id'];
                }
            }

            // Get current product name
            $currentProductName = null;
            if ($currentProductId) {
                $currentProductNameQuery = "SELECT name FROM stripe_products WHERE stripe_product_id = ?";
                $currentProductNameStmt = mysqli_prepare($link, $currentProductNameQuery);

                if ($currentProductNameStmt) {
                    mysqli_stmt_bind_param($currentProductNameStmt, "s", $currentProductId);
                    mysqli_stmt_execute($currentProductNameStmt);
                    $currentProductNameResult = mysqli_stmt_get_result($currentProductNameStmt);
                    $currentProductNameData = mysqli_fetch_assoc($currentProductNameResult);

                    if ($currentProductNameData && isset($currentProductNameData['name'])) {
                        $currentProductName = $currentProductNameData['name'];
                    }
                }
            }

            // Get requested product ID and information
            $requestedProductId = null;
            $requestedProductName = null;
            $requestedPlanInterval = null;
            $requestedPlanAmount = null;

            $requestedPlanQuery = "SELECT sp.stripe_product_id, p.name, pr.unit_amount, pr.`interval`
                                  FROM stripe_prices pr
                                  JOIN stripe_products p ON pr.stripe_product_id = p.stripe_product_id
                                  WHERE pr.stripe_price_id = ?";
            $requestedPlanStmt = mysqli_prepare($link, $requestedPlanQuery);

            if ($requestedPlanStmt) {
                mysqli_stmt_bind_param($requestedPlanStmt, "s", $priceId);
                mysqli_stmt_execute($requestedPlanStmt);
                $requestedPlanResult = mysqli_stmt_get_result($requestedPlanStmt);
                $requestedPlanData = mysqli_fetch_assoc($requestedPlanResult);

                if ($requestedPlanData) {
                    $requestedProductId = $requestedPlanData['stripe_product_id'];
                    $requestedProductName = $requestedPlanData['name'];
                    $requestedPlanInterval = $requestedPlanData['interval'];
                    $requestedPlanAmount = $requestedPlanData['unit_amount'];
                }
            }

            // Determine subscription levels
            $levels = ['free' => 0, 'basic' => 1, 'advance' => 2, 'premium' => 3];

            $currentLevel = determineSubscriptionLevel($currentProductName ?: $activeSubscription['plan_name']);
            $requestedLevel = determineSubscriptionLevel($requestedProductName);

            $currentLevelRank = isset($levels[$currentLevel]) ? $levels[$currentLevel] : 0;
            $requestedLevelRank = isset($levels[$requestedLevel]) ? $levels[$requestedLevel] : 0;

            StripeLogger::log(StripeLogLevel::INFO, "SUBSCRIPTION CHECK - User ID: $userId, Current: $currentProductName (Level: $currentLevel, Rank: $currentLevelRank), Requested: $requestedProductName (Level: $requestedLevel, Rank: $requestedLevelRank)");

            // Always block creating a new subscription if user already has one
            // If the requested plan is a higher level, suggest upgrade
            if ($requestedLevelRank > $currentLevelRank) {
                StripeLogger::log(StripeLogLevel::NOTICE, "USER ALREADY HAS ACTIVE SUBSCRIPTION, SUGGESTING UPGRADE - User ID: $userId, Current Plan: {$activeSubscription['plan_name']}, Current Level: $currentLevel, Requested Plan: $requestedProductName, Requested Level: $requestedLevel");

                echo json_encode(new ErrorResult([
                    'code' => 'subscription_exists',
                    'message' => 'You already have an active subscription. Would you like to upgrade instead?',
                    'currentSubscription' => [
                        'id' => $activeSubscription['id'],
                        'stripe_subscription_id' => $activeSubscription['stripe_subscription_id'],
                        'plan_name' => $activeSubscription['plan_name'],
                        'product_name' => $currentProductName,
                        'plan_amount' => $activeSubscription['plan_amount'] / 100, // Convert from cents to dollars
                        'plan_interval' => $activeSubscription['plan_interval'],
                        'level' => $currentLevel
                    ],
                    'requestedPlan' => [
                        'price_id' => $priceId,
                        'product_id' => $requestedProductId,
                        'name' => $requestedProductName,
                        'amount' => $requestedPlanAmount / 100, // Convert from cents to dollars
                        'interval' => $requestedPlanInterval,
                        'level' => $requestedLevel
                    ]
                ], 409)); // 409 Conflict status code
                return;
            }
            // If the requested plan is the same or lower level, inform the user
            else {
                StripeLogger::log(StripeLogLevel::NOTICE, "USER ALREADY HAS EQUAL OR BETTER SUBSCRIPTION - User ID: $userId, Current Plan: {$activeSubscription['plan_name']}, Current Level: $currentLevel, Requested Plan: $requestedProductName, Requested Level: $requestedLevel");

                echo json_encode(new ErrorResult([
                    'code' => 'subscription_exists',
                    'message' => 'You already have an active subscription that is equal to or better than the requested plan.',
                    'currentSubscription' => [
                        'id' => $activeSubscription['id'],
                        'stripe_subscription_id' => $activeSubscription['stripe_subscription_id'],
                        'plan_name' => $activeSubscription['plan_name'],
                        'product_name' => $currentProductName,
                        'plan_amount' => $activeSubscription['plan_amount'] / 100, // Convert from cents to dollars
                        'plan_interval' => $activeSubscription['plan_interval'],
                        'level' => $currentLevel
                    ],
                    'requestedPlan' => [
                        'price_id' => $priceId,
                        'product_id' => $requestedProductId,
                        'name' => $requestedProductName,
                        'amount' => $requestedPlanAmount / 100, // Convert from cents to dollars
                        'interval' => $requestedPlanInterval,
                        'level' => $requestedLevel
                    ]
                ], 409)); // 409 Conflict status code
                return;
            }
        }

        // Create checkout session parameters
        $sessionParams = [
            'payment_method_types' => ['card'],
            'line_items' => [
                [
                    'price' => $priceId,
                    'quantity' => 1,
                ],
            ],
            'mode' => 'subscription',
            'success_url' => $successUrl,
            'cancel_url' => $cancelUrl,
            'client_reference_id' => $userId,
            'allow_promotion_codes' => true,
            'metadata' => [
                'user_id' => $userId
            ]
        ];

        // If user already has a Stripe customer ID, use it
        if ($stripeCustomerId) {
            $sessionParams['customer'] = $stripeCustomerId;
        } else {
            $sessionParams['customer_email'] = $customerEmail;
        }

        // Create checkout session
        $session = \Stripe\Checkout\Session::create($sessionParams);

        // Log the session creation
        StripeLogger::log(StripeLogLevel::INFO, "STRIPE CHECKOUT SESSION CREATED - User ID: $userId, Session ID: {$session->id}");

        // Return session ID and URL
        echo json_encode(new SuccessResult([
            'sessionId' => $session->id,
            'url' => $session->url
        ]));

    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "STRIPE CHECKOUT ERROR: " . $e->getMessage(), [
            'exception' => get_class($e)
        ]);
        echo json_encode(new ErrorResult($e->getMessage(), 500));
    }
}

/**
 * Handle checkout session completed event
 *
 * @param object $session The checkout session object from Stripe
 */
function handleCheckoutSessionCompleted($session) {
    global $link;

    // Get relevant IDs and information
    $checkoutSessionId = $session->id;
    $clientReferenceId = $session->client_reference_id; // User ID from frontend
    $customerId = $session->customer; // Stripe customer ID
    $subscriptionId = $session->subscription; // Subscription ID
    $email = $session->customer_details->email; // Customer email

    StripeLogger::log(StripeLogLevel::INFO, "STRIPE CHECKOUT COMPLETED - Client Reference ID: $clientReferenceId, Customer ID: $customerId, Email: $email");

    try {
        // Find user
        $userId = null;

        // 1. First try to find user by client_reference_id (user ID from frontend)
        if ($clientReferenceId) {
            $query = "SELECT id FROM users WHERE id = ?";
            $stmt = mysqli_prepare($link, $query);

            if (!$stmt) {
                throw new Exception("Database error: " . mysqli_error($link));
            }

            mysqli_stmt_bind_param($stmt, "i", $clientReferenceId);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $user = mysqli_fetch_assoc($result);

            if ($user) {
                $userId = $user['id'];
                StripeLogger::log(StripeLogLevel::DEBUG, "STRIPE USER FOUND BY CLIENT REFERENCE ID - User ID: $userId");

                // Update user's stripe_customer_id
                if ($customerId) {
                    $updateQuery = "UPDATE users SET stripe_customer_id = ? WHERE id = ?";
                    $updateStmt = mysqli_prepare($link, $updateQuery);

                    if ($updateStmt) {
                        mysqli_stmt_bind_param($updateStmt, "si", $customerId, $userId);
                        mysqli_stmt_execute($updateStmt);
                        StripeLogger::log(StripeLogLevel::INFO, "STRIPE CUSTOMER ID UPDATED - User ID: $userId, Customer ID: $customerId");
                    }
                }
            }
        }

        // 2. If user not found, try to find by stripe_customer_id
        if (!$userId && $customerId) {
            $query = "SELECT id FROM users WHERE stripe_customer_id = ?";
            $stmt = mysqli_prepare($link, $query);

            if (!$stmt) {
                throw new Exception("Database error: " . mysqli_error($link));
            }

            mysqli_stmt_bind_param($stmt, "s", $customerId);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $user = mysqli_fetch_assoc($result);

            if ($user) {
                $userId = $user['id'];
                StripeLogger::log(StripeLogLevel::DEBUG, "STRIPE USER FOUND BY CUSTOMER ID - User ID: $userId");
            }
        }

        // 3. If user still not found and email exists, create new user
        if (!$userId && $customerId && $email) {
            // Get customer information
            $customer = \Stripe\Customer::retrieve($customerId);

            // Create new user
            handleNewCustomer($customer);

            // Get newly created user
            $query = "SELECT id FROM users WHERE stripe_customer_id = ?";
            $stmt = mysqli_prepare($link, $query);

            if (!$stmt) {
                throw new Exception("Database error: " . mysqli_error($link));
            }

            mysqli_stmt_bind_param($stmt, "s", $customerId);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $user = mysqli_fetch_assoc($result);

            if ($user) {
                $userId = $user['id'];
                StripeLogger::log(StripeLogLevel::INFO, "STRIPE NEW USER CREATED FROM CHECKOUT - User ID: $userId");
            }
        }

        // Save checkout session information to database
        $query = "INSERT INTO stripe_checkout_sessions (
            checkout_session_id,
            client_reference_id,
            customer_id,
            subscription_id,
            email,
            created_at
        ) VALUES (?, ?, ?, ?, ?, NOW())";

        $stmt = mysqli_prepare($link, $query);

        if (!$stmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($stmt, "sisss",
            $checkoutSessionId,
            $clientReferenceId,
            $customerId,
            $subscriptionId,
            $email
        );

        if (!mysqli_stmt_execute($stmt)) {
            throw new Exception("Failed to save checkout session: " . mysqli_error($link));
        }

        // If user ID exists and subscription exists, update subscription information
        if ($userId && $subscriptionId) {
            // Get subscription information
            $subscription = \Stripe\Subscription::retrieve($subscriptionId);

            if ($subscription) {
                // First check if this subscription already exists in database
                $checkQuery = "SELECT id FROM stripe_user_subscriptions WHERE stripe_subscription_id = ?";
                $checkStmt = mysqli_prepare($link, $checkQuery);

                if (!$checkStmt) {
                    throw new Exception("Database error: " . mysqli_error($link));
                }

                mysqli_stmt_bind_param($checkStmt, "s", $subscriptionId);
                mysqli_stmt_execute($checkStmt);
                $checkResult = mysqli_stmt_get_result($checkStmt);

                if (mysqli_num_rows($checkResult) > 0) {
                    // Subscription already exists, update it
                    $updateQuery = "UPDATE stripe_user_subscriptions SET
                        status = ?,
                        updated_at = NOW()
                        WHERE stripe_subscription_id = ?";

                    $updateStmt = mysqli_prepare($link, $updateQuery);

                    if (!$updateStmt) {
                        throw new Exception("Database error: " . mysqli_error($link));
                    }

                    $status = $subscription->status;
                    mysqli_stmt_bind_param($updateStmt, "ss", $status, $subscriptionId);

                    if (!mysqli_stmt_execute($updateStmt)) {
                        throw new Exception("Failed to update subscription: " . mysqli_error($link));
                    }

                    StripeLogger::log(StripeLogLevel::INFO, "STRIPE SUBSCRIPTION UPDATED AFTER CHECKOUT - Subscription ID: $subscriptionId, Status: $status");
                } else {
                    // Subscription doesn't exist, it will be created by handleSubscriptionCreated function
                    StripeLogger::log(StripeLogLevel::NOTICE, "STRIPE SUBSCRIPTION NOT FOUND IN DB - Waiting for subscription.created webhook");
                }
            }
        }

        StripeLogger::log(StripeLogLevel::INFO, "STRIPE CHECKOUT SESSION PROCESSED - Session ID: $checkoutSessionId");

    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "STRIPE CHECKOUT ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'checkoutSessionId' => $checkoutSessionId ?? 'unknown'
        ]);
    }
}
