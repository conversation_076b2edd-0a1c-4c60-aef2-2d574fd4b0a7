<?php
/**
 * Stripe Price Functions
 *
 * This file contains functions related to Stripe prices.
 */

// Include required files
require_once __DIR__ . '/StripeLogger.php';

/**
 * Handle price created event
 *
 * @param object $price The price object from Stripe
 */
function handlePriceCreated($price) {
    global $link;

    StripeLogger::log(StripeLogLevel::INFO, "STRIPE PRICE CREATED - ID: {$price->id}, Product: {$price->product}");

    try {
        // Convert metadata to JSON
        $metadata = json_encode($price->metadata);

        // Check if price already exists
        $checkQuery = "SELECT id FROM stripe_prices WHERE stripe_price_id = ?";
        $checkStmt = mysqli_prepare($link, $checkQuery);

        if (!$checkStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($checkStmt, "s", $price->id);
        mysqli_stmt_execute($checkStmt);
        $result = mysqli_stmt_get_result($checkStmt);

        if (mysqli_num_rows($result) > 0) {
            // Price already exists, update it
            $updateQuery = "UPDATE stripe_prices SET
                stripe_product_id = ?,
                currency = ?,
                unit_amount = ?,
                `interval` = ?,
                interval_count = ?,
                active = ?,
                metadata = ?
                WHERE stripe_price_id = ?";

            $updateStmt = mysqli_prepare($link, $updateQuery);

            if (!$updateStmt) {
                throw new Exception("Database error: " . mysqli_error($link));
            }

            $active = $price->active ? 1 : 0;
            $unitAmount = $price->unit_amount / 100; // Convert from cents to dollars
            $interval = isset($price->recurring) ? $price->recurring->interval : null;
            $intervalCount = isset($price->recurring) ? $price->recurring->interval_count : null;

            mysqli_stmt_bind_param(
                $updateStmt,
                "ssdsiss",
                $price->product,
                $price->currency,
                $unitAmount,
                $interval,
                $intervalCount,
                $active,
                $metadata,
                $price->id
            );

            if (!mysqli_stmt_execute($updateStmt)) {
                throw new Exception("Failed to update price: " . mysqli_error($link));
            }

            StripeLogger::log(StripeLogLevel::INFO, "STRIPE PRICE UPDATED IN DB - ID: {$price->id}");
        } else {
            // Insert new price
            $insertQuery = "INSERT INTO stripe_prices (
                stripe_price_id,
                stripe_product_id,
                currency,
                unit_amount,
                `interval`,
                interval_count,
                active,
                metadata
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";

            $insertStmt = mysqli_prepare($link, $insertQuery);

            if (!$insertStmt) {
                throw new Exception("Database error: " . mysqli_error($link));
            }

            $active = $price->active ? 1 : 0;
            $unitAmount = $price->unit_amount / 100; // Convert from cents to dollars
            $interval = isset($price->recurring) ? $price->recurring->interval : null;
            $intervalCount = isset($price->recurring) ? $price->recurring->interval_count : null;

            mysqli_stmt_bind_param(
                $insertStmt,
                "sssdsiis",
                $price->id,
                $price->product,
                $price->currency,
                $unitAmount,
                $interval,
                $intervalCount,
                $active,
                $metadata
            );

            if (!mysqli_stmt_execute($insertStmt)) {
                throw new Exception("Failed to insert price: " . mysqli_error($link));
            }

            StripeLogger::log(StripeLogLevel::INFO, "STRIPE PRICE INSERTED INTO DB - ID: {$price->id}");
        }
    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "STRIPE PRICE CREATION ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'priceId' => $price->id ?? 'unknown'
        ]);
    }
}

/**
 * Handle price updated event
 *
 * @param object $price The price object from Stripe
 */
function handlePriceUpdated($price) {
    global $link;

    StripeLogger::log(StripeLogLevel::INFO, "STRIPE PRICE UPDATED - ID: {$price->id}, Product: {$price->product}");

    try {
        // Convert metadata to JSON
        $metadata = json_encode($price->metadata);

        // Update price in database
        $updateQuery = "UPDATE stripe_prices SET
            stripe_product_id = ?,
            currency = ?,
            unit_amount = ?,
            `interval` = ?,
            interval_count = ?,
            active = ?,
            metadata = ?
            WHERE stripe_price_id = ?";

        $updateStmt = mysqli_prepare($link, $updateQuery);

        if (!$updateStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        $active = $price->active ? 1 : 0;
        $unitAmount = $price->unit_amount / 100; // Convert from cents to dollars
        $interval = isset($price->recurring) ? $price->recurring->interval : null;
        $intervalCount = isset($price->recurring) ? $price->recurring->interval_count : null;

        mysqli_stmt_bind_param(
            $updateStmt,
            "ssdsiss",
            $price->product,
            $price->currency,
            $unitAmount,
            $interval,
            $intervalCount,
            $active,
            $metadata,
            $price->id
        );

        if (!mysqli_stmt_execute($updateStmt)) {
            throw new Exception("Failed to update price: " . mysqli_error($link));
        }

        $affectedRows = mysqli_affected_rows($link);

        if ($affectedRows > 0) {
            StripeLogger::log(StripeLogLevel::INFO, "STRIPE PRICE UPDATED IN DB - ID: {$price->id}");
        } else {
            // Price doesn't exist, create it
            handlePriceCreated($price);
        }
    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "STRIPE PRICE UPDATE ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'priceId' => $price->id ?? 'unknown'
        ]);
    }
}

/**
 * Handle price deleted event
 *
 * @param object $price The price object from Stripe
 */
function handlePriceDeleted($price) {
    global $link;

    StripeLogger::log(StripeLogLevel::WARNING, "STRIPE PRICE DELETED - ID: {$price->id}, Product: {$price->product}");

    try {
        // Delete price from database
        $deleteQuery = "DELETE FROM stripe_prices WHERE stripe_price_id = ?";
        $deleteStmt = mysqli_prepare($link, $deleteQuery);

        if (!$deleteStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($deleteStmt, "s", $price->id);

        if (!mysqli_stmt_execute($deleteStmt)) {
            throw new Exception("Failed to delete price: " . mysqli_error($link));
        }

        $affectedRows = mysqli_affected_rows($link);

        if ($affectedRows > 0) {
            StripeLogger::log(StripeLogLevel::INFO, "STRIPE PRICE DELETED FROM DB - ID: {$price->id}");
        } else {
            StripeLogger::log(StripeLogLevel::NOTICE, "STRIPE PRICE NOT FOUND IN DB - ID: {$price->id}");
        }
    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "STRIPE PRICE DELETION ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'priceId' => $price->id ?? 'unknown'
        ]);
    }
}
