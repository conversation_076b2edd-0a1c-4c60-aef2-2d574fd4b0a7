<?php

$webhookUrl = "https://hook.eu2.make.com/zkb3r5xt4bvvh1kue6fmmdxtdx8fjv8o";

// Sample data payload
$data = [
    'content_id' => 123,
    'translate_lang' => 'turkish',
    'text' => 'Once upon a time, there was a little bird who dreamed of flying higher than the clouds. Every morning, it would practice flapping its wings, never giving up. One day, the wind carried it up, and it soared through the sky. The bird felt free, happy, and proud of its hard work. From that day on, it believed anything was possible with determination.',
    'updated_at' => date('Y-m-d H:i:s'),
];

// Initialize cURL
$ch = curl_init($webhookUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
]);

// Execute cURL and close
$response = curl_exec($ch);

echo $response;

curl_close($ch);

?>