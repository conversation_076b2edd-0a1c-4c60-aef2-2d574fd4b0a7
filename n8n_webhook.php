
<?php
require_once 'secrets.php';
require_once 'config.php';
require_once 'clientmethods/PriceService.php';
require_once 'n8n_helper_functions.php';

header('Content-Type: application/json');

// N8N Secret key kontrolü (Stripe benzeri)
$n8nSecretKey = $n8nWebhookSecret ?? 'your-n8n-secret-key';
$receivedKey = $_SERVER['HTTP_X_N8N_SECRET'] ?? '';

if ($receivedKey !== $n8nSecretKey) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized - Invalid N8N secret']);
    exit;
}

// N8N webhook handler
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);

    error_log("Received N8N request: " . json_encode($input));
    $action = $input['action'] ?? '';
    $data = $input['data'] ?? [];

    if ($action === 'create_portfolio_template') {
        $result = handleCreatePortfolioTemplate($data);
        echo json_encode($result);
    } elseif ($action === 'save_portfolio_template') {
        $result = handleSavePortfolioTemplate($data);
        echo json_encode($result);
    } else {
        $result = [
            'status' => 'error',
            'message' => 'Unknown action: ' . $action,
            'received_action' => $action
        ];
        echo json_encode($result);
    }
} else {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
}

function handleCreatePortfolioTemplate($data) {
    try {
        require_once 'clientmethods/PortfolioScore.php';
        require_once 'clientmethods/CoinDetailMethods.php';
        require_once 'n8n_helper_functions.php';
        require_once 'config.php';
        require_once 'utils.php';


        // N8N'den gelen veriyi portfolio context formatına çevir
        $portfolioContext = convertN8NDataToPortfolioContext($data);

        if (!$portfolioContext) {
            return [
                'status' => 'error',
                'message' => 'Invalid portfolio data format',
                'required_fields' => getRequiredN8NFields()['create_portfolio_template']
            ];
        }


        // Portfolio skorlarını hesapla
        $metrics = build_portfolio_metrics($portfolioContext);

        // Artık create sadece analiz ve hesaplar döner; DB'ye yazma ayrı hook'ta
        return [
            'status' => 'success',
            'message' => 'Portfolio template analyzed successfully',
            'template_name' => $data['template_name'] ?? 'N8N Portfolio',
            'metrics' => $metrics,
            'portfolio' => $portfolioContext,
            'portfolio_summary' => [
                'total_assets' => count($portfolioContext['assets']),
                'total_value' => $portfolioContext['totalValue']
            ]
        ];

    } catch (Exception $e) {
        error_log("Portfolio template error: " . $e->getMessage());
        return [
            'status' => 'error',
            'message' => 'Failed to process portfolio template',
            'error' => $e->getMessage()
        ];
    }
}

function handleSavePortfolioTemplate($data) {
    try {
        require_once 'config.php';
        global $link;

        // N8N'den gelen predefined template verisi
        $templateName = isset($data['name']) ? trim((string)$data['name']) : '';
        $description = isset($data['description']) ? trim((string)$data['description']) : '';
        $riskLevel = isset($data['risk_level']) ? trim((string)$data['risk_level']) : '';
        $timeframe = isset($data['timeframe']) ? trim((string)$data['timeframe']) : '';
        $strategy = isset($data['strategy']) ? trim((string)$data['strategy']) : '';
        $suitableFor = isset($data['suitable_for']) ? $data['suitable_for'] : [];
        $notSuitableFor = isset($data['not_suitable_for']) ? $data['not_suitable_for'] : [];
        $assetsInput = $data['assets'] ?? [];

        // Validation
        if ($templateName === '' || !is_array($assetsInput) || count($assetsInput) === 0) {
            return [
                'status' => 'error',
                'message' => 'Invalid input: name and assets are required'
            ];
        }

        // Risk level validation
        $validRiskLevels = ['low', 'medium', 'high'];
        if (!in_array($riskLevel, $validRiskLevels)) {
            return [
                'status' => 'error',
                'message' => 'Invalid risk_level. Must be one of: ' . implode(', ', $validRiskLevels)
            ];
        }

        // Timeframe validation
        $validTimeframes = ['short', 'medium', 'long'];
        if (!in_array($timeframe, $validTimeframes)) {
            return [
                'status' => 'error',
                'message' => 'Invalid timeframe. Must be one of: ' . implode(', ', $validTimeframes)
            ];
        }

        // Insert predefined template
        $stmt = mysqli_prepare($link, "
            INSERT INTO predefined_portfolio_templates
            (name, description, risk_level, timeframe, strategy, suitable_for, not_suitable_for)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        if (!$stmt) {
            return [ 'status' => 'error', 'message' => 'DB error (prepare template)'];
        }

        $suitableForJson = json_encode($suitableFor);
        $notSuitableForJson = json_encode($notSuitableFor);

        mysqli_stmt_bind_param($stmt, 'sssssss',
            $templateName, $description, $riskLevel, $timeframe, $strategy,
            $suitableForJson, $notSuitableForJson
        );

        if (!mysqli_stmt_execute($stmt)) {
            mysqli_stmt_close($stmt);
            return [ 'status' => 'error', 'message' => 'DB error (execute template)'];
        }
        $templateId = mysqli_insert_id($link);
        mysqli_stmt_close($stmt);

        // Insert assets with percentage allocation
        $stmtA = mysqli_prepare($link, "INSERT INTO predefined_template_assets (template_id, coin_id, percentage) VALUES (?, ?, ?)");
        if ($stmtA) {
            $totalPercentage = 0;
            foreach ($assetsInput as $a) {
                // coin_id ve allocation zorunlu
                if (!isset($a['coin_id']) || !isset($a['allocation'])) {
                    continue;
                }

                $coinId = (int)$a['coin_id'];
                $percentage = (float)$a['allocation'];
                $totalPercentage += $percentage;

                mysqli_stmt_bind_param($stmtA, 'iid', $templateId, $coinId, $percentage);
                mysqli_stmt_execute($stmtA);
            }
            mysqli_stmt_close($stmtA);

            // Percentage validation (should be close to 100%)
            if (abs($totalPercentage - 100.0) > 1.0) {
                error_log("Warning: Template ID $templateId has total percentage of $totalPercentage%, expected ~100%");
            }
        }

        return [
            'status' => 'success',
            'message' => 'Predefined portfolio template saved successfully',
            'template_id' => (int)$templateId,
            'template_name' => $templateName,
            'risk_level' => $riskLevel,
            'timeframe' => $timeframe
        ];

    } catch (Exception $e) {
        error_log("Predefined portfolio template save error: " . $e->getMessage());
        return [
            'status' => 'error',
            'message' => 'Failed to save predefined portfolio template',
            'error' => $e->getMessage()
        ];
    }
}


function getRequiredN8NFields() {
    return [
        // create_portfolio_template için
        'create_portfolio_template' => [
            'template_name' => 'string - Portfolio template name',
            'assets' => 'array - List of portfolio assets',
            'assets[].coin_id' => 'int - Coin database ID (coindata tablosundan)',
            'assets[].allocation' => 'float - Asset allocation percentage (0-100)',
            // Opsiyonel alanlar - yoksa veritabanından çekilir
            'assets[].symbol' => 'string - Coin symbol (optional, DB den çekilir)',
            'assets[].name' => 'string - Coin name (optional, DB den çekilir)',
            'assets[].market_cap' => 'float - Market cap (optional, DB den çekilir)',
            'assets[].ai_score' => 'float - AI score (optional, DB den çekilir)',
            'assets[].is_vetted' => 'bool - Vetted status (optional, DB den çekilir)',
            'assets[].categories' => 'array - Categories (optional, DB den çekilir)'
        ],

        // save_portfolio_template için (predefined templates)
        'save_portfolio_template' => [
            'name' => 'string - Template name (required)',
            'description' => 'string - Template description (optional)',
            'risk_level' => 'enum - Risk level: low, medium, high (required)',
            'timeframe' => 'enum - Investment timeframe: short, medium, long (required)',
            'strategy' => 'string - Investment strategy description (optional)',
            'suitable_for' => 'array - List of suitable investor types (optional)',
            'not_suitable_for' => 'array - List of unsuitable investor types (optional)',
            'assets' => 'array - List of portfolio assets (required)',
            'assets[].coin_id' => 'int - Coin database ID (coindata tablosundan) (required)',
            'assets[].allocation' => 'float - Asset allocation percentage (0-100) (required, total should be ~100%)'
        ]
    ];
}

function convertN8NDataToPortfolioContext($data) {

    if (!isset($data['assets']) || !is_array($data['assets'])) {
        return null;
    }

    $totalValue = 1000000; // 1M USD varsayılan portföy değeri
    $assets = [];

    // Önce hangi veri tipini kullandığımızı belirle
    $useAllocation = false;
    $useAmount = false;

    foreach ($data['assets'] as $asset) {
        if (isset($asset['allocation'])) {
            $useAllocation = true;
            break;
        }
        if (isset($asset['amount'])) {
            $useAmount = true;
            break;
        }
    }

    // Eğer amount kullanıyorsak, önce toplam amount'ı hesapla
    $totalAmount = 0;
    if ($useAmount && !$useAllocation) {
        foreach ($data['assets'] as $asset) {
            if (isset($asset['amount']) && isset($asset['coin_id'])) {
                $totalAmount += (float)$asset['amount'];
            }
        }
    }

    foreach ($data['assets'] as $asset) {
        // coin_id zorunlu
        if (!isset($asset['coin_id'])) {
            continue;
        }

        $coinId = (int)$asset['coin_id'];

        // allocation veya amount'tan allocation hesapla
        if ($useAllocation && isset($asset['allocation'])) {
            $allocation = (float)$asset['allocation'];
            $assetValue = ($allocation / 100) * $totalValue;
        } elseif ($useAmount && isset($asset['amount']) && $totalAmount > 0) {
            $amount = (float)$asset['amount'];
            $allocation = ($amount / $totalAmount) * 100;
            $assetValue = ($allocation / 100) * $totalValue;
        } else {
            continue; // Geçersiz veri
        }

        // Veritabanından coin bilgilerini çek
        $coinData = fetchCoinDataFromDB($coinId);
        if (!$coinData) {
            continue; // Geçersiz coin_id
        }

        // Fiyatı id ile çek (geckoslug=false)
        $currentPrice = get_current_price_by_coin_id($coinId, ['isGeckoSlug' => false]);
        if (!$currentPrice || $currentPrice <= 0) {
            // Yedek: DB helper'ın döndürdüğü current_price varsa onu kullan
            $currentPrice = $coinData['current_price'] ?? null;
        }

        // Eğer amount kullanıyorsak, gelen amount'ı kullan; allocation kullanıyorsak hesapla
        if ($useAmount && isset($asset['amount'])) {
            $calculatedAmount = (float)$asset['amount'];
        } else {
            $calculatedAmount = $currentPrice && $currentPrice > 0 ? ($assetValue / $currentPrice) : 0;
        }

        $assets[] = [
            'coinId' => $coinId,
            'symbol' => $asset['symbol'] ?? $coinData['symbol'],
            'name' => $asset['name'] ?? $coinData['name'],
            'allocation' => $allocation,
            'currentPrice' => $currentPrice,
            'currentValue' => $assetValue,
            'totalInvested' => $assetValue,
            'amount' => $calculatedAmount,
            'marketCap' => (float)($asset['market_cap'] ?? $coinData['marketcap'] ?? 0),
            'aiScore' => (float)($asset['ai_score'] ?? $coinData['total_score'] ?? 50),
            'isVetted' => (bool)($asset['is_vetted'] ?? $coinData['is_vetted'] ?? false),
            'categories' => $asset['categories'] ?? $coinData['categories'] ?? [],
            'fdv' => $coinData['fdv'] ?? null,
            'volume24h' => $coinData['total_volume'] ?? null,
            'change24h' => $coinData['price_change_1d'] ?? null,
            'change7d' => $coinData['price_change_7d'] ?? null
        ];
    }

    return [
        'assets' => $assets,
        'totalValue' => $totalValue,
        'assetCount' => count($assets)
    ];
}
?>
