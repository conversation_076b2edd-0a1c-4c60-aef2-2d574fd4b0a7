
<?php



cors();
$is_debug = true;

//For security purposes, we can allow only specified agent type
$user_agent = $_SERVER['HTTP_USER_AGENT']; 
if (preg_match('/python/i', $user_agent)) { 
    
    
    echo 'You are forbidden!';

    foreach ($_POST as $key => $value) {
        echo "a";
        error_log("Python Script Istegi : ". $key."=".$value,0);
     
    }

    header('HTTP/1.0 403 Forbidden');

return;

 }




//$uri = $_SERVER['REQUEST_URI'];   // /api/users
//$method = $_SERVER['REQUEST_METHOD'];  // GET,POST,DELETE, etc.






include_once('config.php');


$payload = file_get_contents('php://input');


$data = json_decode($payload,true);

if($data == null)
{

    $debug_info = "";
    if($is_debug)
    $debug_info = "Payload is not a valid json or null";



    http_response_code(404);
    echo json_encode(['error' => "Invalid Request!", 'detail' => $debug_info]);
    return;
}



$functName = "";

if (isset($data['f']) )
{
$functName = $data['f'];
}
else
{
    $debug_info = "";
    if($is_debug)
    $debug_info = "Endpoint variable is not set";



    http_response_code(404);
    echo json_encode(['error' => "Invalid Request!", 'detail' => $debug_info]);
    return;  
}




$validEndpoints = array("get_coin_result");


if (in_array($functName, $validEndpoints)) {

    if ($functName == 'get_coin_result') {
        $functName();
    }




}
else
{
    $debug_info = "";
    if($is_debug)
    $debug_info = "Endpoint Not Exists";



    http_response_code(404);
    echo json_encode(['error' => "Invalid Request!", 'detail' => $debug_info]);
}






function get_coin_result()
{

    ini_set('memory_limit', '-1');
    set_time_limit(0);
    
    global $link;

       //coindata includes all coin general data,
    //so we are going to ask detailed coin data for each of that table items
    $our_data = array();
    $obj2 = array();
    $rs = mysqli_query($link, "SELECT geckoid FROM `coindata` where id > 139800");
    while ($obj2 = mysqli_fetch_assoc($rs)) {
        $our_data[] = $obj2;
    }



    $geckoid = "";

    for($i=0; $i < sizeof($our_data); $i++)
    {
        $geckoid .=  $our_data[$i]['geckoid']." , ";

        if($i % 400 == 0 && $i > 0)
        {

            echo $geckoid;
            return;

            chatgpt_request($geckoid);
            $geckoid = "";
        }

      

    }

    if (strlen($geckoid) > 0)
    {
        echo $geckoid;
            return;
        chatgpt_request($geckoid);
    }



}


function chatgpt_request($geckoid)
{
    global $link;

    $apiKey = '********************************************************************************************************************************************************************';



    $veri = [
        "model" => "gpt-4",
        "messages" => [
            ["role" => "system", "content" => "Sen bir veritabanı asistanısın."],

            ["role" => "user", "content" => "virgul ile ayırarak verdigim coinlerin, kripto projesinin
             ekibinin anonim olup olmadığını araştırıp eger anonim ise 1, anonim değil ise  
             0 olarak cevapla. extra açıklama istemiyorum ve cümle sonunda nokta da olmasın.
              cevabı da insert into ekip_bilgi (geckoid, answer) values ('coininadı', verdigin cevap); 
              şeklinde oluştur tek satır halinde oluştur.".$geckoid],
      
           // ["role" => "user", "content" => $geckoid." adlı kripto projesinin ekibinin anonim olup olmadığını araştırıp true ya da false olarak cevapla. extra exıklama isteimyorum sadece true ve false cevabını kabul ediyorum ve cümle sonunda nokta da olmasın"],
        ]
    ];
    
    
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://api.openai.com/v1/chat/completions');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $apiKey,
    ]);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($veri));
    
    $response = curl_exec($ch);
    if (curl_errno($ch)) {
        echo 'Curl hatası: ' . curl_error($ch);
    } else {
        echo 'ChatGPT cevabı: ' . $response;
    }
    curl_close($ch);
    
    
    
    
    
    $cevap = json_decode($response, true);
    $cevap_icerik = $cevap['choices'][0]['message']['content'];
    $cevap_icerik = str_replace("\n", '', $cevap_icerik);
    
    
    $sorgu_id = "1";
    $call = mysqli_prepare($link, 'insert into chatgpt_results (geckoid, answer  ) 
    values (?, ?)');
    mysqli_stmt_bind_param($call, 'ss', 
    $sorgu_id, $cevap_icerik
    );
    $rs = (mysqli_stmt_execute($call));
    
}


function cors()
{
    // Allow from any origin
    if (isset($_SERVER['HTTP_ORIGIN'])) {
        header("Access-Control-Allow-Origin: *");
        header("Access-Control-Allow-Methods: GET, POST, PATCH, PUT, DELETE, OPTIONS");
        header("Access-Control-Allow-Headers: Origin, Authorization, X-Requested-With, Content-Type, Accept");
        header('Access-Control-Allow-Credentials: true');
        header('Access-Control-Max-Age: 86400');    // cache for 1 day
    }

    // Access-Control headers are received during OPTIONS requests
    if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
        if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_METHOD'])) {
            // may also be using PUT, PATCH, HEAD etc
            header("Access-Control-Allow-Methods: GET, POST, PATCH, PUT, DELETE, OPTIONS");
        }

        if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS'])) {
            header("Access-Control-Allow-Headers: Origin, Authorization, X-Requested-With, Content-Type, Accept");
        }

        exit(0);
    }
}





?>