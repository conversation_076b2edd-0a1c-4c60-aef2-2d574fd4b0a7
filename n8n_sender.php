<?php
require_once 'config.php';

class N8NSender {
    private $n8nWebhookUrl;

    public function __construct($webhookUrl) {
        $this->n8nWebhookUrl = $webhookUrl;
    }

    public function sendToN8N($action, $data) {
        $payload = [
            'action' => $action,
            'data' => $data,
            'timestamp' => time(),
            'source' => 'coinscout_backend'
        ];

        $ch = curl_init($this->n8nWebhookUrl);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'User-Agent: CoinScout-Backend/1.0'
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 600);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        return [
            'success' => $httpCode === 200,
            'response' => $response,
            'http_code' => $httpCode
        ];
    }

    // Demo metodu - gerçek işlemler daha sonra eklenecek
    public function sendTest($message) {
        return $this->sendToN8N('send_test', [
            'message' => $message,
            'test_data' => 'demo'
        ]);
    }
}

// Kullanım örneği:
// $n8n = new N8NSender('https://your-n8n-instance.com/webhook/coinscout');
// $n8n->sendTest('Test message');
?>