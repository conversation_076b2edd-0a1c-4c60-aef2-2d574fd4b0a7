/**
 * Logout Integration Example
 * Proper logout handling with WebSocket disconnection
 */

class AuthManager {
    constructor() {
        this.wsClient = null;
        this.authToken = null;
    }

    // Initialize WebSocket connection after login
    initWebSocket(authToken) {
        this.authToken = authToken;
        
        if (this.wsClient) {
            this.wsClient.disconnect();
        }

        this.wsClient = new CoinScoutWebSocketClient(
            'ws://localhost:8080',
            authToken,
            { debug: true }
        );

        // Set up event listeners
        this.wsClient.on('auth_success', () => {
            console.log('✅ WebSocket authenticated');
        });

        this.wsClient.on('notification', (notification) => {
            this.handleNewNotification(notification);
        });

        this.wsClient.on('error', (error) => {
            console.error('❌ WebSocket error:', error);
        });

        this.wsClient.connect();
    }

    // Proper logout function
    async logout() {
        try {
            console.log('🔄 Logging out...');

            // 1. Call logout API
            const response = await fetch('/authentication.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.authToken}`
                },
                body: JSON.stringify({
                    f: 'logout_user'
                })
            });

            const result = await response.json();

            if (result.success) {
                console.log('✅ Server logout successful');

                // 2. Disconnect WebSocket immediately
                if (this.wsClient) {
                    console.log('🔌 Disconnecting WebSocket...');
                    this.wsClient.disconnect();
                    this.wsClient = null;
                }

                // 3. Clear local storage
                localStorage.removeItem('auth_token');
                sessionStorage.clear();

                // 4. Clear auth token
                this.authToken = null;

                // 5. Redirect to login page
                window.location.href = '/login';

                console.log('✅ Logout completed successfully');
            } else {
                throw new Error(result.message || 'Logout failed');
            }
        } catch (error) {
            console.error('❌ Logout error:', error);
            
            // Even if API fails, clean up locally
            if (this.wsClient) {
                this.wsClient.disconnect();
                this.wsClient = null;
            }
            localStorage.removeItem('auth_token');
            this.authToken = null;
            
            // Still redirect to login
            window.location.href = '/login';
        }
    }

    // Handle new notifications
    handleNewNotification(notification) {
        // Show notification to user
        this.showNotificationToUser(notification);
        
        // Update notification counter
        this.updateNotificationCounter();
    }

    showNotificationToUser(notification) {
        // Implementation depends on your UI framework
        console.log('📬 New notification:', notification);
        
        // Example: Show browser notification
        if (Notification.permission === 'granted') {
            new Notification(notification.title, {
                body: notification.message,
                icon: '/assets/notification-icon.png'
            });
        }
    }

    updateNotificationCounter() {
        // Update UI notification counter
        // Implementation depends on your UI framework
    }
}

// React Hook for logout
function useAuth() {
    const [authManager] = useState(() => new AuthManager());
    const [isAuthenticated, setIsAuthenticated] = useState(false);

    const login = useCallback(async (email, password, remember = false) => {
        try {
            const response = await fetch('/authentication.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    f: 'login_user',
                    email,
                    password,
                    remember
                })
            });

            const result = await response.json();

            if (result.success) {
                const token = result.output.token;
                localStorage.setItem('auth_token', token);
                
                // Initialize WebSocket
                authManager.initWebSocket(token);
                setIsAuthenticated(true);
                
                return { success: true, token };
            } else {
                return { success: false, error: result.errormsg };
            }
        } catch (error) {
            return { success: false, error: error.message };
        }
    }, [authManager]);

    const logout = useCallback(async () => {
        await authManager.logout();
        setIsAuthenticated(false);
    }, [authManager]);

    // Check if user is authenticated on mount
    useEffect(() => {
        const token = localStorage.getItem('auth_token');
        if (token) {
            authManager.initWebSocket(token);
            setIsAuthenticated(true);
        }
    }, [authManager]);

    return {
        isAuthenticated,
        login,
        logout,
        authManager
    };
}

// Vanilla JavaScript usage
const authManager = new AuthManager();

// Login example
async function handleLogin(email, password, remember = false) {
    try {
        const response = await fetch('/authentication.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                f: 'login_user',
                email,
                password,
                remember
            })
        });

        const result = await response.json();

        if (result.success) {
            const token = result.output.token;
            localStorage.setItem('auth_token', token);
            
            // Initialize WebSocket
            authManager.initWebSocket(token);
            
            console.log('✅ Login successful');
            return true;
        } else {
            console.error('❌ Login failed:', result.errormsg);
            return false;
        }
    } catch (error) {
        console.error('❌ Login error:', error);
        return false;
    }
}

// Logout example
async function handleLogout() {
    await authManager.logout();
}

// Page unload cleanup
window.addEventListener('beforeunload', () => {
    if (authManager.wsClient) {
        authManager.wsClient.disconnect();
    }
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { AuthManager, useAuth };
}

// Global usage
if (typeof window !== 'undefined') {
    window.AuthManager = AuthManager;
    window.useAuth = useAuth;
    window.handleLogin = handleLogin;
    window.handleLogout = handleLogout;
}
