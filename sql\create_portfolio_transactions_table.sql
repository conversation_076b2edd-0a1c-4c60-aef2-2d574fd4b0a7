-- Portfolio Transactions Table
-- Stores buy/sell operations for assets (asset-level transactions)

CREATE TABLE IF NOT EXISTS portfolio_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    asset_id INT NOT NULL, -- References portfolio_coins.id
    type ENUM('buy','sell') NOT NULL,
    amount DECIMAL(36, 18) NOT NULL,
    price DECIMAL(24, 10) NOT NULL,
    fees DECIMAL(24, 10) DEFAULT 0,
    notes TEXT NULL,
    executed_at DATETIME NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX (asset_id),
    INDEX (executed_at),
    INDEX idx_asset_executed_at (asset_id, executed_at),

    CONSTRAINT fk_tx_asset FOREIGN KEY (asset_id) REFERENCES portfolio_coins(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
