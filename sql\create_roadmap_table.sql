-- Create roadmap_data table for storing roadmap items
CREATE TABLE roadmap_data (
    id INT AUTO_INCREMENT PRIMARY KEY,
    slug VARCHAR(255) UNIQUE NOT NULL,
    title VARCHAR(500) NOT NULL,
    description TEXT,
    status ENUM('completed', 'in-progress', 'planned') NOT NULL DEFAULT 'planned',
    category ENUM('feature', 'enhancement', 'bug-fix', 'integration') NOT NULL,
    priority ENUM('high', 'medium', 'low') NOT NULL DEFAULT 'medium',
    eta DATE,
    tags JSON,
    assigned_to VARCHAR(255) NULL,
    completed_date DATE NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- <PERSON><PERSON><PERSON><PERSON> performans için
CREATE INDEX idx_slug ON roadmap_data(slug);
CREATE INDEX idx_status ON roadmap_data(status);
CREATE INDEX idx_category ON roadmap_data(category);
CREATE INDEX idx_priority ON roadmap_data(priority);
CREATE INDEX idx_assigned_to ON roadmap_data(assigned_to);
CREATE INDEX idx_eta ON roadmap_data(eta);

-- Tags JSON alanı için arama indeksi (MySQL 8.0+ için)
CREATE INDEX idx_tags ON roadmap_data((CAST(tags AS CHAR(255) ARRAY)));
