-- chat_conversations tablosuna name sütunu ekleme
ALTER TABLE chat_conversations
ADD COLUMN name VA<PERSON><PERSON><PERSON>(255) NULL AFTER user_id;



ALTER TABLE chat_conversations
ADD COLUMN uuid CHAR(36) NULL;



UPDATE chat_conversations
SET uuid = UUID()
WHERE uuid IS NULL OR uuid = '';



--TRIGGER
DELIMITER //

CREATE TRIGGER before_insert_chat_conversations
BEFORE INSERT ON chat_conversations
FOR EACH ROW
BEGIN
  IF NEW.uuid IS NULL OR NEW.uuid = '' THEN
    SET NEW.uuid = UUID();
  END IF;
END//

DELIMITER ;

