-- Remove unused status column from user_notifications table
-- The status column was redundant with is_read column

-- Check if status column exists before dropping
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'user_notifications'
    AND COLUMN_NAME = 'status'
);

-- Drop the column if it exists
SET @sql = IF(@column_exists > 0,
    'ALTER TABLE user_notifications DROP COLUMN status',
    'SELECT "Status column does not exist" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Verify the change
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'user_notifications'
ORDER BY ORDINAL_POSITION;
