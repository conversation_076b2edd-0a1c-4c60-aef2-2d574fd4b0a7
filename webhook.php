<?php
$webhookUrl = "https://hook.eu2.make.com/n2mcr1d5isir97hay1sqznlixvpmhbra";






if(!isset($_GET['isresponse']))
{
// Sample data payload
$data = [
    'content_id' => 123,
    'english_text' => 'Once upon a time, there was a little bird who dreamed of flying higher than the clouds. Every morning, it would practice flapping its wings, never giving up. One day, the wind carried it up, and it soared through the sky. The bird felt free, happy, and proud of its hard work. From that day on, it believed anything was possible with determination.',
    'updated_at' => date('Y-m-d H:i:s'),
];

// Initialize cURL
$ch = curl_init($webhookUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
]);

// Execute cURL and close
$response = curl_exec($ch);

echo $response;

curl_close($ch);


error_log("new web hook",0);
}
else
{
    $payload = file_get_contents('php://input');


$data = json_decode($payload, true);

if ($data == null) {

    $debug_info = "";
    if ($is_debug)
        $debug_info = "Payload is not a valid json or null";



    http_response_code(404);
    echo json_encode(['error' => "Invalid Request!", 'detail' => $debug_info]);
    error_log("Payload invalid = ".$payload,0);
    return;
}




    error_log($payload,0);
}
?>