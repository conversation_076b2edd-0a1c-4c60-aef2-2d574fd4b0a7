<?php

/**
 * Portfolio Management Methods (static mock implementation)
 *
 * Note: These endpoints return static data for now. We'll wire real DB later.
 * Responses use SuccessResult/ErrorResult wrappers as in the rest of the API.
 */
function now_iso()
{
    return date(DATE_ATOM);
}

function parse_iso_to_datetime($iso)
{
    $ts = strtotime($iso ?? '');
    if ($ts === false) {
        return null;
    }
    return date('Y-m-d H:i:s', $ts);
}

/**
 * Compute current position for an asset based on transactions (weighted-average cost method)
 * - Buys increase quantity and cost basis (includes fees)
 * - Sells decrease quantity and reduce cost basis at current avg cost (sale price does not change avg cost)
 * Returns [qty, avg_cost, cost_basis]
 */
function compute_asset_position($link, $assetId, $seedQty = 0.0, $seedAvgCost = 0.0)
{
    $qty = (float)$seedQty;
    $avgCost = (float)$seedAvgCost;
    $costBasis = $qty * $avgCost;
    $stmt = mysqli_prepare($link, "SELECT type, amount, price, fees FROM portfolio_transactions WHERE asset_id = ? ORDER BY executed_at ASC, id ASC");
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, 'i', $assetId);
        if (mysqli_stmt_execute($stmt)) {
            $res = mysqli_stmt_get_result($stmt);
            if ($res) {
                while ($row = mysqli_fetch_assoc($res)) {
                    $type = strtolower((string)$row['type']);
                    $amt = (float)$row['amount'];
                    $price = (float)$row['price'];
                    $fees = isset($row['fees']) ? (float)$row['fees'] : 0.0;
                    if ($amt <= 0) continue;
                    if ($type === 'buy') {
                        $buyCost = ($amt * $price) + $fees;
                        $newQty = $qty + $amt;
                        if ($newQty > 0) {
                            $avgCost = ($costBasis + $buyCost) / $newQty;
                        }
                        $qty = $newQty;
                        $costBasis += $buyCost;
                    } elseif ($type === 'sell') {
                        $sellQty = min($amt, $qty);
                        if ($sellQty > 0) {
                            $costReduction = $avgCost * $sellQty;
                            $qty -= $sellQty;
                            $costBasis -= $costReduction;
                            if ($qty <= 0) {
                                $qty = 0.0;
                                $avgCost = 0.0;
                                $costBasis = 0.0;
                            }
                        }
                    }
                }
            }
        }
        mysqli_stmt_close($stmt);
    }
    return [$qty, $avgCost, $costBasis];
}
function get_coin_geckoslug($link, $coinId)
{
    $slug = null;
    $stmt = mysqli_prepare($link, "SELECT geckoslug FROM coindata WHERE id = ? LIMIT 1");
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, 'i', $coinId);
        if (mysqli_stmt_execute($stmt)) {
            $res = mysqli_stmt_get_result($stmt);
            $row = $res ? mysqli_fetch_assoc($res) : null;
            $slug = $row['geckoslug'] ?? null;
        }
        mysqli_stmt_close($stmt);
    }
    return $slug;
}

function compute_portfolio_totals_with_prices($link, $portfolioId)
{
    $totalInvested = 0.0;
    $totalValue = 0.0;
    $assetCount = 0;
    $stmt = mysqli_prepare($link, "SELECT id AS asset_id, coin_id FROM portfolio_coins WHERE portfolio_id = ?");
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, 'i', $portfolioId);
        if (mysqli_stmt_execute($stmt)) {
            $res = mysqli_stmt_get_result($stmt);
            if ($res) {
                while ($r = mysqli_fetch_assoc($res)) {
                    $assetId = (int)$r['asset_id'];
                    $coinId = (int)$r['coin_id'];
                    list($qty, $avgCost, $costBasis) = compute_asset_position($link, $portfolioId, $assetId);
                    if ($qty > 0) {
                        $assetCount += 1;
                    }
                    $totalInvested += $costBasis;
                    $slug = get_coin_geckoslug($link, $coinId);
                    $price = null;
                    if ($slug) {
                        require_once __DIR__ . '/PriceService.php';
                        $price = get_current_price_by_coin_id($slug, ['isGeckoSlug' => true]);
                    }
                    if ($price !== null && $price > 0) {
                        $totalValue += (float)$price * (float)$qty;
                    } else {
                        $totalValue += $costBasis;
                    }
                }
            }
        }
        mysqli_stmt_close($stmt);
    }
    return [$totalInvested, $totalValue, $assetCount];
}

/**
 * Check if portfolio metrics need update (60 minute check)
 */
function should_update_portfolio_metrics($link, $portfolioId)
{
    $stmt = mysqli_prepare($link, "SELECT updated_at FROM portfolio_scores_history WHERE portfolio_id = ? ORDER BY updated_at DESC LIMIT 1");
    if (!$stmt) return true;

    mysqli_stmt_bind_param($stmt, 'i', $portfolioId);
    if (!mysqli_stmt_execute($stmt)) {
        mysqli_stmt_close($stmt);
        return true;
    }

    $res = mysqli_stmt_get_result($stmt);
    $row = $res ? mysqli_fetch_assoc($res) : null;
    mysqli_stmt_close($stmt);

    if (!$row) return true;

    $lastUpdate = strtotime($row['updated_at']);
    $now = time();
    return ($now - $lastUpdate) >= 3600; // 60 minutes
}

/**
 * Save portfolio metrics to database
 */
function save_portfolio_metrics($link, $portfolioId, $metrics)
{
    if (!should_update_portfolio_metrics($link, $portfolioId)) {
        return;
    }

    $category_diversification_score = isset($metrics['category_diversification']['score']) ? (float)$metrics['category_diversification']['score'] : 0;
    $trend_category_score = isset($metrics['trend_category']['score']) ? (float)$metrics['trend_category']['score'] : 0;
    $market_cap_distribution_score = isset($metrics['marketCapDistribution']['score']) ? (float)$metrics['marketCapDistribution']['score'] : 0;
    $portfolio_weight_score = isset($metrics['portfolio_weight']['score']) ? (float)$metrics['portfolio_weight']['score'] : 0;
    $stablecoinRatioScore = isset($metrics['stablecoinRatio']['score']) ? (float)$metrics['stablecoinRatio']['score'] : 0;
    $portfolio_quality_score = isset($metrics['portfolio_quality']['score']) ? (float)$metrics['portfolio_quality']['score'] : 0;
    $totalScore = isset($metrics['totalScore']) ? (float)$metrics['totalScore'] : 0;

    $stmt = mysqli_prepare(
        $link,
        "INSERT INTO portfolio_scores_history
        (portfolio_id, category_diversification_score, trend_category_score, market_cap_distribution_score, portfolio_weight_score, stablecoinRatioScore, portfolio_quality_score, totalScore)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)"
    );

    if ($stmt) {
        mysqli_stmt_bind_param(
            $stmt,
            'iddddddd',
            $portfolioId,
            $category_diversification_score,
            $trend_category_score,
            $market_cap_distribution_score,
            $portfolio_weight_score,
            $stablecoinRatioScore,
            $portfolio_quality_score,
            $totalScore
        );
        mysqli_stmt_execute($stmt);
        mysqli_stmt_close($stmt);
    }
}


/* ============================
 * Portfolio CRUD
 * ============================ */
function getPortfolios($userId, $filters = null)
{
    global $link, $selectedLanguage, $clientMessages;
    $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';
    $stmt = mysqli_prepare($link, "SELECT id, name, description FROM portfolio WHERE user_id = ?");
    if (!$stmt) {
        (new ErrorResult($clientMessages[$lang]['failed_to_retrieve_portfolios']))->send(500);
        return;
    }
    mysqli_stmt_bind_param($stmt, 'i', $userId);
    if (!mysqli_stmt_execute($stmt)) {
        mysqli_stmt_close($stmt);
        (new ErrorResult($clientMessages[$lang]['failed_to_retrieve_portfolios']))->send(500);
        return;
    }
    $result = mysqli_stmt_get_result($stmt);
    $rows = [];
    if ($result) {
        while ($row = mysqli_fetch_assoc($result)) {
            $rows[] = $row;
        }
    }
    mysqli_stmt_close($stmt);
    $now = now_iso();
    $list = [];
    foreach ($rows as $row) {
        $pid = (int)$row['id'];
        list($totInvested, $totValue, $assetCount) = compute_portfolio_totals_with_prices($link, $pid);
        $profitPct = $totInvested > 0 ? round((($totValue - $totInvested) / $totInvested) * 100, 2) : 0;
        $list[] = [
            'id' => (string)$row['id'],
            'name' => $row['name'],
            'description' => $row['description'],
            'icon' => 'layers',
            'theme' => 'blue',
            'totalValue' => $totValue,
            'totalInvested' => $totInvested,
            'profitPercentage' => $profitPct,
            'dayChangePercentage' => 0.0,
            'overallScore' => 0,
            'assetCount' => $assetCount,
            'isPublic' => false,
            'updatedAt' => $now
        ];
    }
    (new SuccessResult($list, ['timestamp' => $now, 'count' => count($list)]))->send();
}
function getPortfolio($userId, $id)
{
    global $link;
    global $selectedLanguage, $clientMessages;
    $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';
    // 1) Fetch portfolio core fields
    $stmt = mysqli_prepare($link, "SELECT id, name, description FROM portfolio WHERE id = ? AND user_id = ? LIMIT 1");
    if (!$stmt) {
        (new ErrorResult($clientMessages[$lang]['failed_to_retrieve_portfolios']))->send(500);
        return;
    }
    mysqli_stmt_bind_param($stmt, 'ii', $id, $userId);
    if (!mysqli_stmt_execute($stmt)) {
        mysqli_stmt_close($stmt);
        (new ErrorResult($clientMessages[$lang]['failed_to_retrieve_portfolios']))->send(500);
        return;
    }
    $res = mysqli_stmt_get_result($stmt);
    $portfolioRow = $res ? mysqli_fetch_assoc($res) : null;
    mysqli_stmt_close($stmt);
    if (!$portfolioRow) {
        (new ErrorResult('Portfolio not found.'))->send(404);
        return;
    }
    // 2) Fetch assets from portfolio_coins and join coin metadata (keep all fields; fill what we can)
    $stmt2 = mysqli_prepare($link, "SELECT pc.id as asset_id, pc.coin_id, pc.average_cost, pc.amount, pc.is_vetted,
            c.name as coin_name, c.symbol as coin_symbol, c.image as coin_image, c.geckoslug as coin_geckoslug,
            c.geckoid as coin_geckoid, c.total_score as coin_total_score,
            c.marketcap as coin_marketcap, c.fdv as coin_fdv, c.total_volume as coin_volume,
            c.circulating_supply as circ_supply, c.total_supply as total_supply, c.max_supply as max_supply,
            c2.price_change_1d as p1d, c2.price_change_7d as p7d, c2.coin_age as coin_age, c2.ico_price as ico_price
        FROM portfolio_coins pc
        LEFT JOIN coindata c ON c.id = pc.coin_id
        LEFT JOIN coindata2 c2 ON c.geckoid = c2.geckoid
        WHERE pc.portfolio_id = ?");
    if (!$stmt2) {
        (new ErrorResult($clientMessages[$lang]['failed_to_retrieve_portfolios']))->send(500);
        return;
    }
    mysqli_stmt_bind_param($stmt2, 'i', $id);
    if (!mysqli_stmt_execute($stmt2)) {
        mysqli_stmt_close($stmt2);
        (new ErrorResult($clientMessages[$lang]['failed_to_retrieve_portfolios']))->send(500);
        return;
    }
    $assetRes = mysqli_stmt_get_result($stmt2);
    $assets = [];
    if ($assetRes) {
        while ($row = mysqli_fetch_assoc($assetRes)) {
            // Use transaction history to compute live position (qty, avgCost, costBasis)
            $assetIdInt = (int)$row['asset_id'];
            $hasTx = false;
            $stmtTxCnt = mysqli_prepare($link, "SELECT COUNT(*) AS cnt FROM portfolio_transactions WHERE asset_id = ?");
            if ($stmtTxCnt) {
                mysqli_stmt_bind_param($stmtTxCnt, 'i', $assetIdInt);
                if (mysqli_stmt_execute($stmtTxCnt)) {
                    $txRes = mysqli_stmt_get_result($stmtTxCnt);
                    $txRow = $txRes ? mysqli_fetch_assoc($txRes) : null;
                    $hasTx = ((int)($txRow['cnt'] ?? 0)) > 0;
                }
                mysqli_stmt_close($stmtTxCnt);
            }
            // Başlangıç pozisyonu olarak portfolio_coins.amount ve average_cost'ı seed olarak kullan
            $seedQty = (float)($row['amount'] ?? 0);
            $seedAvg = (float)($row['average_cost'] ?? 0);
            list($qtyCalc, $avgCalc, $basisCalc) = compute_asset_position($link, $assetIdInt, $seedQty, $seedAvg);
            if (!$hasTx) {
                // Transaction yoksa seed değerleri zaten kullanılacak
                $qtyCalc = $seedQty;
                $avgCalc = $seedAvg;
                $basisCalc = $seedQty * $seedAvg;
            }
            $amount = $qtyCalc;
            $avgCost = $avgCalc;
            $totalInvested = $basisCalc;
            $isVetted = isset($row['is_vetted']) ? ((int)$row['is_vetted'] === 1) : false;
            $marketCap = isset($row['coin_marketcap']) ? (float)$row['coin_marketcap'] : null;
            $fdv = isset($row['coin_fdv']) ? (float)$row['coin_fdv'] : null;
            $volume24h = isset($row['coin_volume']) ? (float)$row['coin_volume'] : null;
            $circSupply = isset($row['circ_supply']) ? (float)$row['circ_supply'] : null;
            $totalSupply = isset($row['total_supply']) ? (float)$row['total_supply'] : null;
            $marketCapToFdv = ($fdv && $fdv != 0) ? round($marketCap / $fdv, 2) : null;
            $circSupplyPercent = ($totalSupply && $totalSupply != 0 && $circSupply !== null) ? round(($circSupply / $totalSupply) * 100, 1) : null;
            $p1d = isset($row['p1d']) ? (float)$row['p1d'] : null;
            $p7d = isset($row['p7d']) ? (float)$row['p7d'] : null;
            // metric scores
            $tokenomics = null;
            $security = null;
            $social = null;
            $market = null;
            $insights = null;
            $totalScore = isset($row['coin_total_score']) ? (float)$row['coin_total_score'] : null;
            if ($isVetted && !empty($row['coin_geckoid'])) {
                // group scores only for vetted coins
                $qGroups = "SELECT metric_group, score FROM coin_group_scores_all WHERE geckoid = '" . mysqli_real_escape_string($link, $row['coin_geckoid']) . "' AND metric_group IN (1,3,4,5,6)";
                $rGroups = mysqli_query($link, $qGroups);
                if ($rGroups && mysqli_num_rows($rGroups) > 0) {
                    while ($gr = mysqli_fetch_assoc($rGroups)) {
                        $mg = (int)$gr['metric_group'];
                        $sc = isset($gr['score']) ? (float)$gr['score'] : null;
                        if ($mg === 1) $tokenomics = $sc;
                        else if ($mg === 3) $security = $sc;
                        else if ($mg === 4) $social = $sc;
                        else if ($mg === 5) $market = $sc;
                        else if ($mg === 6) $insights = $sc;
                    }
                }
            }
            // Fetch portfolio-usable categories for this coin (by geckoid) - max 5, ordered by market_cap
            $categories = [];
            if (!empty($row['coin_geckoid'])) {
                $stmtCat = mysqli_prepare($link, "SELECT cc.category_name, COALESCE(gc.market_cap, 0) as market_cap, gc.geckoid as gid FROM coindata_categories cc LEFT JOIN gecko_categories gc ON cc.category_name = gc.name WHERE cc.geckoid = ? AND COALESCE(gc.market_cap, 0) > 0 ORDER BY COALESCE(gc.market_cap, 0) DESC LIMIT 5");
                if ($stmtCat) {
                    $geckoidStr = (string)$row['coin_geckoid'];
                    mysqli_stmt_bind_param($stmtCat, 's', $geckoidStr);
                    if (mysqli_stmt_execute($stmtCat)) {
                        $resCat = mysqli_stmt_get_result($stmtCat);
                        if ($resCat) {
                            while ($cr = mysqli_fetch_assoc($resCat)) {
                                if (!empty($cr['category_name'])) {
                                    $categories[] = [
                                        'name' => (string)$cr['category_name'],
                                        'marketCap' => (float)$cr['market_cap'],
                                        'gid' => $cr['gid'] ?? null
                                    ];
                                }
                            }
                        }
                    }
                    mysqli_stmt_close($stmtCat);
                }
            }

            // Fallback: If no categories found with geckoid, try with geckoslug
            if (empty($categories) && !empty($row['coin_geckoslug'])) {
                $stmtCat2 = mysqli_prepare($link, "SELECT cc.category_name, COALESCE(gc.market_cap, 0) as market_cap, gc.geckoid as gid FROM coindata_categories cc LEFT JOIN gecko_categories gc ON cc.category_name = gc.name WHERE cc.geckoid = ? AND COALESCE(gc.market_cap, 0) > 0 ORDER BY COALESCE(gc.market_cap, 0) DESC LIMIT 5");
                if ($stmtCat2) {
                    $geckoslugStr = (string)$row['coin_geckoslug'];
                    mysqli_stmt_bind_param($stmtCat2, 's', $geckoslugStr);
                    if (mysqli_stmt_execute($stmtCat2)) {
                        $resCat2 = mysqli_stmt_get_result($stmtCat2);
                        if ($resCat2) {
                            while ($cr2 = mysqli_fetch_assoc($resCat2)) {
                                if (!empty($cr2['category_name'])) {
                                    $categories[] = [
                                        'name' => (string)$cr2['category_name'],
                                        'marketCap' => (float)$cr2['market_cap'],
                                        'gid' => $cr2['gid'] ?? null
                                    ];
                                }
                            }
                        }
                    }
                    mysqli_stmt_close($stmtCat2);
                }
            }
            $assets[] = [
                'id' => (string)$row['asset_id'],
                'coinId' => (string)$row['coin_id'],
                'isVetted' => $isVetted,
                'symbol' => $row['coin_symbol'] ?? null,
                'name' => $row['coin_name'] ?? null,
                'logoUrl' => $row['coin_image'] ?? null,
                'amount' => $amount,
                'avgCost' => $avgCost,
                'totalInvested' => $totalInvested,
                // Vetting-independent fields from coindata/coindata2
                'currentPrice' => null,
                'currentValue' => null,
                'allocation' => null,
                'profitLoss' => null,
                'profitLossPercentage' => null,
                'change24h' => $p1d,
                'change7d' => $p7d,
                'volume24h' => $volume24h,
                'marketCap' => $marketCap,
                'fdv' => $fdv,
                'marketCapToFdv' => $marketCapToFdv,
                'circSupply' => $circSupply,
                'circSupplyPercent' => $circSupplyPercent,
                'totalSupply' => $totalSupply,
                'coinAge' => isset($row['coin_age']) ? (float)$row['coin_age'] : null,
                'listingDate' => isset($row['coin_age']) && $row['coin_age'] > 0 ? date('Y-m-d', (int)$row['coin_age']) : null,
                // Vetting-dependent additional analytics (null if not vetted)
                'aiScore' => $totalScore,
                'security' => $isVetted ? $security : null,
                'social' => $isVetted ? $social : null,
                'market' => $isVetted ? $market : null,
                'tokenomics' => $isVetted ? $tokenomics : null,
                'insights' => $isVetted ? $insights : null,
                // Categories for trend/sector scoring (from coindata_categories.use_for_portfolio=1)
                'categories' => $categories,
                'color' => null,
                'createdAt' => now_iso(),
                'updatedAt' => now_iso()
            ];
        }
    }
    mysqli_stmt_close($stmt2);
    // Fetch dynamic prices only if vetted (has geckoslug)
    $totalInvested = 0.0; $totalValue = 0.0;
    foreach ($assets as &$a) {
        $totalInvested += (float)($a['totalInvested'] ?? 0);
        if (!empty($a['coinId'])) {
            $slug = get_coin_geckoslug($link, (int)$a['coinId']);
            if ($slug) {
                require_once __DIR__ . '/PriceService.php';
                $price = get_current_price_by_coin_id($slug, ['isGeckoSlug' => true]);
                if ($price !== null && $price > 0) {
                    $a['currentPrice'] = (float)$price;
                    $a['currentValue'] = round($price * (float)($a['amount'] ?? 0), 8);
                    $a['profitLoss'] = round(((float)$a['currentValue'] - (float)$a['totalInvested']), 8);
                    $a['profitLossPercentage'] = (float)($a['totalInvested'] ?? 0) > 0 ? round(($a['profitLoss'] / (float)$a['totalInvested']) * 100, 2) : 0;
                    $totalValue += (float)$a['currentValue'];
                } else {
                    $totalValue += (float)($a['totalInvested'] ?? 0);
                }
            } else {
                // No slug -> use invested as value
                $totalValue += (float)($a['totalInvested'] ?? 0);
            }
        }
    }


    // Compute allocations based on currentValue if available; otherwise based on invested
    $denominator = $totalValue > 0 ? $totalValue : ($totalInvested > 0 ? $totalInvested : 0);
    if ($denominator > 0) {
        foreach ($assets as &$a) {
            $basis = isset($a['currentValue']) && $a['currentValue'] !== null ? (float)$a['currentValue'] : (float)($a['totalInvested'] ?? 0);
            $a['allocation'] = round(($basis / $denominator) * 100, 2);
        }
        unset($a);
    }
    $now = now_iso();
    // Build simulated metrics using static scoring helper
    require_once __DIR__ . '/PortfolioScore.php';
    $context = [
        'assets' => $assets,
        'assetCount' => count($assets),
        'totalValue' => $totalValue,
        'totalInvested' => $totalInvested
    ];
    $metrics = build_portfolio_metrics($context);
    $overallScoreCalc = isset($metrics['totalScore']) ? (float)$metrics['totalScore'] : 0.0;

    // Save metrics to database (with 60-minute check)
    save_portfolio_metrics($link, $id, $metrics);

    // Calculate category distribution percentages for frontend chart
    $categoryDiversification = compute_category_distribution_percentages($context);

    // Calculate market cap distribution percentages for frontend chart
    $marketCapDistribution = compute_market_cap_distribution_breakdown($context);

    // Calculate trending categories based on sum of asset allocations per category
    $trendingCategories = compute_trending_categories($context);

    $portfolio = [
        'id' => (string)$portfolioRow['id'],
        'name' => $portfolioRow['name'],
        'description' => $portfolioRow['description'],
        'icon' => 'layers', // not persisted yet
        'theme' => 'blue',  // not persisted yet
        'totalValue' => $totalValue,
        'totalInvested' => $totalInvested,
        'profit' => $totalValue - $totalInvested,
        'profitPercentage' => $totalInvested > 0 ? round((($totalValue - $totalInvested) / $totalInvested) * 100, 2) : 0,
        'dayChange' => 0,
        'dayChangePercentage' => 0,
        'overallScore' => $overallScoreCalc,
        'category_diversification' => $categoryDiversification,
        'market_cap_distribution' => $marketCapDistribution,
        'trending_categories' => $trendingCategories,
        'stablecoin_ratio' => compute_stablecoin_ratio_details($context),
        'metrics' => $metrics,
        'assets' => $assets,
        'assetCount' => count($assets),
        'isPublic' => false,
        'isActive' => true,
        'createdAt' => $now,
        'updatedAt' => $now,
        'lastPriceUpdate' => $now
    ];
    (new SuccessResult($portfolio))->send();
}
function createPortfolio($userId, $payload)
{
    global $link, $selectedLanguage, $clientMessages;
    // Extract and validate input (only name is required for DB)
    $name = isset($payload['name']) ? trim((string)$payload['name']) : '';
    $description = isset($payload['description']) ? trim((string)$payload['description']) : null;
    // Optional client-only fields (not persisted yet)
    $icon = $payload['icon'] ?? 'layers';
    $theme = $payload['theme'] ?? 'default';
    $isPublic = (bool)($payload['isPublic'] ?? false);
    $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';
    if ($name === '') {
        (new ErrorResult($clientMessages[$lang]['invalid_request']))->send(422);
        return;
    }
    // Insert into DB: only user_id, name, description
    $sql = "INSERT INTO portfolio (user_id, name, description) VALUES (?, ?, ?)";
    $stmt = mysqli_prepare($link, $sql);
    if (!$stmt) {
        (new ErrorResult($clientMessages[$lang]['database_error']))->send(500);
        return;
    }
    // Bind params; allow NULL description
    mysqli_stmt_bind_param($stmt, 'iss', $userId, $name, $description);
    $ok = mysqli_stmt_execute($stmt);
    if (!$ok) {
        (new ErrorResult($clientMessages[$lang]['database_error']))->send(500);
        return;
    }
    $insertId = mysqli_insert_id($link);
    mysqli_stmt_close($stmt);
    // Build response object including non-persisted fields for future use
    $now = now_iso();
    $portfolio = [
        'id' => (string)$insertId,
        'userId' => (string)$userId,
        'name' => $name,
        'description' => $description,
        // Client-facing extras (not stored yet)
        'icon' => $icon,
        'theme' => $theme,
        'isPublic' => $isPublic,
        // Default computed fields
        'totalValue' => 0,
        'totalInvested' => 0,
        'profit' => 0,
        'profitPercentage' => 0,
        'dayChange' => 0,
        'dayChangePercentage' => 0,
        'metrics' => [
            'category_diversification' => ['score' => 0, 'contribution' => 0, 'maxScore' => 100, 'analysis' => 'No data'],
            'trend_category' => ['score' => 0, 'contribution' => 0, 'maxScore' => 100, 'analysis' => 'No data'],
            'marketCapDistribution' => ['score' => 0, 'contribution' => 0, 'maxScore' => 100, 'analysis' => 'No data'],
            'portfolio_weight_score' => ['score' => 0, 'contribution' => 0, 'maxScore' => 100, 'analysis' => 'No data'],
            'stablecoinRatio' => ['score' => 0, 'contribution' => 0, 'maxScore' => 100, 'analysis' => 'No data'],
            'portfolio_quality' => ['score' => 0, 'contribution' => 0, 'maxScore' => 100, 'analysis' => 'No data'],
            'totalScore' => 0
        ],
        'assets' => [],
        'assetCount' => 0,
        'isActive' => true,
        'createdAt' => $now,
        'updatedAt' => $now,
        'lastPriceUpdate' => $now
    ];
    (new SuccessResult($portfolio))->send(201);
}
function updatePortfolio($userId, $id, $payload)
{
    global $link, $selectedLanguage, $clientMessages;

    $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';
    $portfolioId = (int)$id;

    $name = isset($payload['name']) ? trim((string)$payload['name']) : null;
    $description = isset($payload['description']) ? trim((string)$payload['description']) : null;

    if ($name === null && $description === null) {
        (new ErrorResult($clientMessages[$lang]['invalid_request']))->send(422);
        return;
    }

    // Verify portfolio belongs to user
    $stmtCheck = mysqli_prepare($link, "SELECT name, description FROM portfolio WHERE id = ? AND user_id = ? LIMIT 1");
    if (!$stmtCheck) {
        (new ErrorResult($clientMessages[$lang]['database_error']))->send(500);
        return;
    }

    mysqli_stmt_bind_param($stmtCheck, 'ii', $portfolioId, $userId);
    if (!mysqli_stmt_execute($stmtCheck)) {
        mysqli_stmt_close($stmtCheck);
        (new ErrorResult($clientMessages[$lang]['database_error']))->send(500);
        return;
    }

    $result = mysqli_stmt_get_result($stmtCheck);
    $existing = $result ? mysqli_fetch_assoc($result) : null;
    mysqli_stmt_close($stmtCheck);

    if (!$existing) {
        (new ErrorResult($clientMessages[$lang]['failed_to_retrieve_portfolios']))->send(404);
        return;
    }

    // Update portfolio
    $sql = "UPDATE portfolio SET ";
    $params = [];
    $types = '';

    if ($name !== null) {
        $sql .= "name = ?, ";
        $params[] = $name;
        $types .= 's';
    }

    if ($description !== null) {
        $sql .= "description = ?, ";
        $params[] = $description;
        $types .= 's';
    }

    $sql = rtrim($sql, ', ') . " WHERE id = ? AND user_id = ?";
    $params[] = $portfolioId;
    $params[] = $userId;
    $types .= 'ii';

    $stmt = mysqli_prepare($link, $sql);
    if (!$stmt) {
        (new ErrorResult($clientMessages[$lang]['database_error']))->send(500);
        return;
    }

    mysqli_stmt_bind_param($stmt, $types, ...$params);
    if (!mysqli_stmt_execute($stmt)) {
        mysqli_stmt_close($stmt);
        (new ErrorResult($clientMessages[$lang]['database_error']))->send(500);
        return;
    }

    mysqli_stmt_close($stmt);

    (new SuccessResult(['success' => true]))->send();
}
function deletePortfolio($userId, $id)
{
    global $link, $selectedLanguage, $clientMessages;

    $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';
    $portfolioId = (int)$id;

    // Verify portfolio belongs to user
    $stmtCheck = mysqli_prepare($link, "SELECT 1 FROM portfolio WHERE id = ? AND user_id = ? LIMIT 1");
    if (!$stmtCheck) {
        (new ErrorResult($clientMessages[$lang]['database_error']))->send(500);
        return;
    }

    mysqli_stmt_bind_param($stmtCheck, 'ii', $portfolioId, $userId);
    if (!mysqli_stmt_execute($stmtCheck)) {
        mysqli_stmt_close($stmtCheck);
        (new ErrorResult($clientMessages[$lang]['database_error']))->send(500);
        return;
    }

    $result = mysqli_stmt_get_result($stmtCheck);
    $exists = $result && mysqli_fetch_assoc($result);
    mysqli_stmt_close($stmtCheck);

    if (!$exists) {
        (new ErrorResult($clientMessages[$lang]['failed_to_retrieve_portfolios']))->send(404);
        return;
    }

    // Delete portfolio assets first
    $stmtAssets = mysqli_prepare($link, "DELETE FROM portfolio_coins WHERE portfolio_id = ?");
    if ($stmtAssets) {
        mysqli_stmt_bind_param($stmtAssets, 'i', $portfolioId);
        mysqli_stmt_execute($stmtAssets);
        mysqli_stmt_close($stmtAssets);
    }

    // Delete portfolio
    $stmt = mysqli_prepare($link, "DELETE FROM portfolio WHERE id = ? AND user_id = ?");
    if (!$stmt) {
        (new ErrorResult($clientMessages[$lang]['database_error']))->send(500);
        return;
    }

    mysqli_stmt_bind_param($stmt, 'ii', $portfolioId, $userId);
    if (!mysqli_stmt_execute($stmt)) {
        mysqli_stmt_close($stmt);
        (new ErrorResult($clientMessages[$lang]['database_error']))->send(500);
        return;
    }

    mysqli_stmt_close($stmt);

    (new SuccessResult(['success' => true]))->send();
}
/* ============================
 * Asset management
 * ============================ */
function addAsset($userId, $portfolioId, $payload)
{
    global $link, $selectedLanguage, $clientMessages;
    require_once __DIR__ . '/../clientmethods/CoinDetailMethods.php';
    $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';
    // Normalize and validate inputs
    $portfolioId = (int)$portfolioId;
    $coinId = isset($payload['coinId']) ? (int)$payload['coinId'] : null;
    $amount = isset($payload['amount']) ? (float)$payload['amount'] : null;
    // Accept both averageCost and avgCost for compatibility
    $avgCost = isset($payload['averageCost']) ? (float)$payload['averageCost'] : ((isset($payload['avgCost']) ? (float)$payload['avgCost'] : null));
    $note = isset($payload['note']) ? trim((string)$payload['note']) : null;
    if (!$coinId || $amount === null || $avgCost === null) {
        (new ErrorResult($clientMessages[$lang]['invalid_request']))->send(422);
        return;
    }
    // Verify portfolio belongs to user
    $stmtCheck = mysqli_prepare($link, "SELECT 1 FROM portfolio WHERE id = ? AND user_id = ? LIMIT 1");
    if (!$stmtCheck) {
        (new ErrorResult($clientMessages[$lang]['database_error']))->send(500);
        return;
    }
    mysqli_stmt_bind_param($stmtCheck, 'ii', $portfolioId, $userId);
    if (!mysqli_stmt_execute($stmtCheck)) {
        mysqli_stmt_close($stmtCheck);
        (new ErrorResult($clientMessages[$lang]['database_error']))->send(500);
        return;
    }
    $resCheck = mysqli_stmt_get_result($stmtCheck);
    $exists = $resCheck && mysqli_fetch_assoc($resCheck);
    mysqli_stmt_close($stmtCheck);
    if (!$exists) {
        (new ErrorResult($clientMessages[$lang]['failed_to_retrieve_portfolios']))->send(404);
        return;
    }

    // Validate asset id and determine is_vetted flag
    $isVetted = null; // 1 if in client_coin_list, 0 if only in coindata, null if not found
    $stmtV = mysqli_prepare($link, "SELECT 1 FROM client_coin_list WHERE id = ? LIMIT 1");
    if ($stmtV) {
        mysqli_stmt_bind_param($stmtV, 'i', $coinId);
        if (mysqli_stmt_execute($stmtV)) {
            $resV = mysqli_stmt_get_result($stmtV);
            if ($resV && mysqli_fetch_assoc($resV)) { $isVetted = 1; }
        }
        mysqli_stmt_close($stmtV);
    }
    if ($isVetted === null) {
        $stmtC = mysqli_prepare($link, "SELECT 1 FROM coindata WHERE id = ? LIMIT 1");
        if ($stmtC) {
            mysqli_stmt_bind_param($stmtC, 'i', $coinId);
            if (mysqli_stmt_execute($stmtC)) {
                $resC = mysqli_stmt_get_result($stmtC);
                if ($resC && mysqli_fetch_assoc($resC)) { $isVetted = 0; }
            }
            mysqli_stmt_close($stmtC);
        }
    }
    if ($isVetted === null) {
        (new ErrorResult($clientMessages[$lang]['invalid_request']))->send(422);
        return;
    }

    // Insert into portfolio_coins with is_vetted
    $stmt = mysqli_prepare($link, "INSERT INTO portfolio_coins (portfolio_id, coin_id, average_cost, amount, transaction_date, note, is_vetted, added_at) VALUES (?, ?, ?, ?, NULL, ?, ?, NOW())");
    if (!$stmt) {
        (new ErrorResult($clientMessages[$lang]['database_error']))->send(500);
        return;
    }
    mysqli_stmt_bind_param($stmt, 'iiddsi', $portfolioId, $coinId, $avgCost, $amount, $note, $isVetted);
    if (!mysqli_stmt_execute($stmt)) {
        mysqli_stmt_close($stmt);
        (new ErrorResult($clientMessages[$lang]['database_error']))->send(500);
        return;
    }
    $assetId = mysqli_insert_id($link);
    mysqli_stmt_close($stmt);
    (new SuccessResult(['success' => true]))->send(201);
}
function updateAsset($userId, $portfolioId, $assetId, $payload)
{
    (new SuccessResult(['success' => true]))->send();
}
function removeAsset($userId, $portfolioId, $assetId)
{
    global $link, $selectedLanguage, $clientMessages;
    $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

    $portfolioId = (int)$portfolioId;
    $assetId = (int)$assetId;

    // Verify asset belongs to user's portfolio
    $stmtCheck = mysqli_prepare($link, "SELECT pc.id FROM portfolio_coins pc JOIN portfolio p ON p.id = pc.portfolio_id WHERE pc.id = ? AND p.user_id = ? AND pc.portfolio_id = ? LIMIT 1");
    if (!$stmtCheck) {
        (new ErrorResult($clientMessages[$lang]['database_error']))->send(500);
        return;
    }

    mysqli_stmt_bind_param($stmtCheck, 'iii', $assetId, $userId, $portfolioId);
    if (!mysqli_stmt_execute($stmtCheck)) {
        mysqli_stmt_close($stmtCheck);
        (new ErrorResult($clientMessages[$lang]['database_error']))->send(500);
        return;
    }

    $resCheck = mysqli_stmt_get_result($stmtCheck);
    $exists = $resCheck && mysqli_fetch_assoc($resCheck);
    mysqli_stmt_close($stmtCheck);

    if (!$exists) {
        (new ErrorResult($clientMessages[$lang]['failed_to_retrieve_portfolios']))->send(404);
        return;
    }

    // Delete related transactions first
    $stmtTx = mysqli_prepare($link, "DELETE FROM portfolio_transactions WHERE asset_id = ?");
    if ($stmtTx) {
        mysqli_stmt_bind_param($stmtTx, 'i', $assetId);
        mysqli_stmt_execute($stmtTx);
        mysqli_stmt_close($stmtTx);
    }

    // Delete the asset
    $stmt = mysqli_prepare($link, "DELETE FROM portfolio_coins WHERE id = ?");
    if (!$stmt) {
        (new ErrorResult($clientMessages[$lang]['database_error']))->send(500);
        return;
    }

    mysqli_stmt_bind_param($stmt, 'i', $assetId);
    if (!mysqli_stmt_execute($stmt)) {
        mysqli_stmt_close($stmt);
        (new ErrorResult($clientMessages[$lang]['database_error']))->send(500);
        return;
    }

    mysqli_stmt_close($stmt);

    (new SuccessResult(['success' => true]))->send();
}
/* ============================
 * Transactions
 * ============================ */
function addTransaction($userId, $portfolioId, $payload)
{
    global $link, $selectedLanguage, $clientMessages;
    $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

    // $portfolioId parametresi kullanılmayacak, sadece assetId üzerinden işlem yapılır
    $assetId = isset($payload['assetId']) ? (int)$payload['assetId'] : null;
    $type = isset($payload['type']) ? strtolower(trim((string)$payload['type'])) : '';
    $amount = isset($payload['amount']) ? (float)$payload['amount'] : null;
    $price = isset($payload['price']) ? (float)$payload['price'] : null;
    $fees = isset($payload['fees']) ? (float)$payload['fees'] : 0.0;
    $notes = isset($payload['notes']) ? trim((string)$payload['notes']) : null;
    $executedAtIso = $payload['executedAt'] ?? null;

    if (!$assetId || !in_array($type, ['buy','sell']) || $amount === null || $amount <= 0 || $price === null || $price < 0) {
        (new ErrorResult($clientMessages[$lang]['invalid_request']))->send(422);
        return;
    }

    // Access kontrol: asset bu kullanıcıya ait bir portföyde mi?
    $stmtCheck = mysqli_prepare($link, "SELECT pc.id FROM portfolio_coins pc JOIN portfolio p ON p.id = pc.portfolio_id WHERE pc.id = ? AND p.user_id = ? LIMIT 1");
    if (!$stmtCheck) { (new ErrorResult($clientMessages[$lang]['database_error']))->send(500); return; }
    mysqli_stmt_bind_param($stmtCheck, 'ii', $assetId, $userId);
    if (!mysqli_stmt_execute($stmtCheck)) { mysqli_stmt_close($stmtCheck); (new ErrorResult($clientMessages[$lang]['database_error']))->send(500); return; }
    $resCheck = mysqli_stmt_get_result($stmtCheck);
    $exists = $resCheck && mysqli_fetch_assoc($resCheck);
    mysqli_stmt_close($stmtCheck);
    if (!$exists) { (new ErrorResult($clientMessages[$lang]['invalid_request']))->send(422); return; }

    // asset’ın portföye ait olmadığını ayrıca kontrol etmeye gerek yok, yukarıdaki JOIN yeterli

    // If selling, ensure sufficient quantity based on seed (portfolio_coins) + existing transactions
    if ($type === 'sell') {
        $seedQty = 0.0; $seedAvg = 0.0;
        $stmtSeed = mysqli_prepare($link, "SELECT amount, average_cost FROM portfolio_coins WHERE id = ? LIMIT 1");
        if ($stmtSeed) {
            mysqli_stmt_bind_param($stmtSeed, 'i', $assetId);
            if (mysqli_stmt_execute($stmtSeed)) {
                $seedRes = mysqli_stmt_get_result($stmtSeed);
                $seedRow = $seedRes ? mysqli_fetch_assoc($seedRes) : null;
                if ($seedRow) {
                    $seedQty = (float)($seedRow['amount'] ?? 0);
                    $seedAvg = (float)($seedRow['average_cost'] ?? 0);
                }
            }
            mysqli_stmt_close($stmtSeed);
        }
        list($qtyNow) = compute_asset_position($link, $assetId, $seedQty, $seedAvg);
        if ($amount > $qtyNow + 1e-12) { // allow tiny epsilon
            (new ErrorResult($clientMessages[$lang]['invalid_request']))->send(422);
            return;
        }
    }

    $executedAt = parse_iso_to_datetime($executedAtIso) ?? date('Y-m-d H:i:s');

    $stmt = mysqli_prepare($link, "INSERT INTO portfolio_transactions (asset_id, type, amount, price, fees, notes, executed_at) VALUES (?, ?, ?, ?, ?, ?, ?)");
    if (!$stmt) {
        (new ErrorResult($clientMessages[$lang]['database_error']))->send(500);
        return;
    }
    mysqli_stmt_bind_param($stmt, 'isdddss', $assetId, $type, $amount, $price, $fees, $notes, $executedAt);
    if (!mysqli_stmt_execute($stmt)) {
        mysqli_stmt_close($stmt);
        (new ErrorResult($clientMessages[$lang]['database_error']))->send(500);
        return;
    }
    $txId = mysqli_insert_id($link);
    mysqli_stmt_close($stmt);

    $tx = [
        'id' => (string)$txId,
        'assetId' => (string)$assetId,
        'type' => $type,
        'amount' => (float)$amount,
        'price' => (float)$price,
        'totalValue' => (float)$amount * (float)$price,
        'fees' => (float)$fees,
        'notes' => $notes,
        'executedAt' => date(DATE_ATOM, strtotime($executedAt)),
        'createdAt' => now_iso()
    ];
    (new SuccessResult($tx))->send(201);
}
function getTransactions($userId, $portfolioId, $assetId = null, $page = 1, $limit = 10)
{
    global $link, $selectedLanguage, $clientMessages;
    $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

    $assetId = $assetId !== null ? (int)$assetId : null;
    $page = max(1, (int)$page);
    $limit = max(1, min(100, (int)$limit));
    $offset = ($page - 1) * $limit;

    // Access kontrol: asset, bu kullanıcının bir portföyüne ait mi?
    if ($assetId === null) {
        (new ErrorResult($clientMessages[$lang]['invalid_request']))->send(422);
        return;
    }
    $stmtCheck = mysqli_prepare($link, "SELECT pc.id FROM portfolio_coins pc JOIN portfolio p ON p.id = pc.portfolio_id WHERE pc.id = ? AND p.user_id = ? LIMIT 1");
    if (!$stmtCheck) {
        (new ErrorResult($clientMessages[$lang]['database_error']))->send(500);
        return;
    }
    mysqli_stmt_bind_param($stmtCheck, 'ii', $assetId, $userId);
    if (!mysqli_stmt_execute($stmtCheck)) {
        mysqli_stmt_close($stmtCheck);
        (new ErrorResult($clientMessages[$lang]['database_error']))->send(500);
        return;
    }
    $resCheck = mysqli_stmt_get_result($stmtCheck);
    $exists = $resCheck && mysqli_fetch_assoc($resCheck);
    mysqli_stmt_close($stmtCheck);
    if (!$exists) {
        (new ErrorResult($clientMessages[$lang]['failed_to_retrieve_portfolios']))->send(404);
        return;
    }

    // Count total
    $stmtCnt = mysqli_prepare($link, "SELECT COUNT(*) AS cnt FROM portfolio_transactions WHERE asset_id = ?");
    if (!$stmtCnt) { (new ErrorResult($clientMessages[$lang]['database_error']))->send(500); return; }
    mysqli_stmt_bind_param($stmtCnt, 'i', $assetId);
    if (!mysqli_stmt_execute($stmtCnt)) { mysqli_stmt_close($stmtCnt); (new ErrorResult($clientMessages[$lang]['database_error']))->send(500); return; }
    $resCnt = mysqli_stmt_get_result($stmtCnt);
    $cntRow = $resCnt ? mysqli_fetch_assoc($resCnt) : ['cnt' => 0];
    $total = (int)($cntRow['cnt'] ?? 0);
    mysqli_stmt_close($stmtCnt);

    // Fetch items
    $stmt = mysqli_prepare($link, "SELECT id, asset_id, type, amount, price, fees, notes, executed_at, created_at FROM portfolio_transactions WHERE asset_id = ? ORDER BY executed_at DESC, id DESC LIMIT ? OFFSET ?");
    if (!$stmt) { (new ErrorResult($clientMessages[$lang]['database_error']))->send(500); return; }
    mysqli_stmt_bind_param($stmt, 'iii', $assetId, $limit, $offset);
    if (!mysqli_stmt_execute($stmt)) { mysqli_stmt_close($stmt); (new ErrorResult($clientMessages[$lang]['database_error']))->send(500); return; }
    $res = mysqli_stmt_get_result($stmt);

    $items = [];
    if ($res) {
        while ($row = mysqli_fetch_assoc($res)) {
            $items[] = [
                'id' => (string)$row['id'],
                'assetId' => (string)$row['asset_id'],
                'type' => $row['type'],
                'amount' => (float)$row['amount'],
                'price' => (float)$row['price'],
                'totalValue' => (float)$row['amount'] * (float)$row['price'],
                'fees' => isset($row['fees']) ? (float)$row['fees'] : 0.0,
                'notes' => $row['notes'] ?? null,
                'executedAt' => date(DATE_ATOM, strtotime($row['executed_at'])),
                'createdAt' => date(DATE_ATOM, strtotime($row['created_at']))
            ];
        }
    }
    mysqli_stmt_close($stmt);

    $totalPages = max(1, (int)ceil($total / $limit));
    $meta = [
        'pagination' => [
            'page' => (int)$page,
            'limit' => (int)$limit,
            'total' => (int)$total,
            'totalPages' => (int)$totalPages
        ],
        'generatedAt' => now_iso()
    ];
    (new SuccessResult($items, $meta))->send();
}
/* ============================
 * Transaction History (Single Asset)
 * ============================ */
function getTransactionHistory($userId, $portfolioId, $assetId = null, $type = null, $dateFromIso = null, $dateToIso = null, $sortBy = 'executedAt', $sortOrder = 'desc', $page = 1, $limit = 10)
{
    global $link, $selectedLanguage, $clientMessages;
    $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';

    $portfolioId = $portfolioId !== null ? (int)$portfolioId : null; // optional, only for access check
    $assetId = $assetId !== null ? (int)$assetId : null;
    $type = $type !== null ? strtolower(trim((string)$type)) : null;
    $page = max(1, (int)$page);
    $limit = max(1, min(100, (int)$limit));
    $offset = ($page - 1) * $limit;

    // Require assetId
    if ($assetId === null) {
        (new ErrorResult($clientMessages[$lang]['invalid_request']))->send(422);
        return;
    }

    // Access control: asset belongs to a portfolio of this user; if portfolioId provided, ensure it matches
    if ($portfolioId !== null) {
        $stmtCheck = mysqli_prepare($link, "SELECT 1 FROM portfolio_coins pc JOIN portfolio p ON p.id = pc.portfolio_id WHERE pc.id = ? AND p.user_id = ? AND pc.portfolio_id = ? LIMIT 1");
        if (!$stmtCheck) { (new ErrorResult($clientMessages[$lang]['database_error']))->send(500); return; }
        mysqli_stmt_bind_param($stmtCheck, 'iii', $assetId, $userId, $portfolioId);
    } else {
        $stmtCheck = mysqli_prepare($link, "SELECT 1 FROM portfolio_coins pc JOIN portfolio p ON p.id = pc.portfolio_id WHERE pc.id = ? AND p.user_id = ? LIMIT 1");
        if (!$stmtCheck) { (new ErrorResult($clientMessages[$lang]['database_error']))->send(500); return; }
        mysqli_stmt_bind_param($stmtCheck, 'ii', $assetId, $userId);
    }
    if (!mysqli_stmt_execute($stmtCheck)) { mysqli_stmt_close($stmtCheck); (new ErrorResult($clientMessages[$lang]['database_error']))->send(500); return; }
    $resCheck = mysqli_stmt_get_result($stmtCheck);
    $exists = $resCheck && mysqli_fetch_assoc($resCheck);
    mysqli_stmt_close($stmtCheck);
    if (!$exists) { (new ErrorResult($clientMessages[$lang]['failed_to_retrieve_portfolios']))->send(404); return; }

    // Date filters
    $dateFrom = $dateFromIso ? parse_iso_to_datetime($dateFromIso) : null;
    $dateTo = $dateToIso ? parse_iso_to_datetime($dateToIso) : null;

    // Sort mapping
    $sortMap = [
        'executedAt' => 'executed_at',
        'amount' => 'amount',
        'quantity' => 'amount',
        'price' => 'price',
        'fees' => 'fees'
    ];
    $orderCol = isset($sortMap[$sortBy]) ? $sortMap[$sortBy] : 'executed_at';
    $orderDir = strtolower($sortOrder) === 'asc' ? 'ASC' : 'DESC';

    // Count total with filters
    $countSql = "SELECT COUNT(*) AS cnt FROM portfolio_transactions WHERE asset_id = ?";
    $countTypes = 'i';
    $countParams = [$assetId];
    if ($type !== null && $type !== '' && $type !== 'all') { $countSql .= " AND type = ?"; $countTypes .= 's'; $countParams[] = $type; }
    if ($dateFrom !== null) { $countSql .= " AND executed_at >= ?"; $countTypes .= 's'; $countParams[] = $dateFrom; }
    if ($dateTo !== null) { $countSql .= " AND executed_at <= ?"; $countTypes .= 's'; $countParams[] = $dateTo; }

    $stmtCnt = mysqli_prepare($link, $countSql);
    if (!$stmtCnt) { (new ErrorResult($clientMessages[$lang]['database_error']))->send(500); return; }
    mysqli_stmt_bind_param($stmtCnt, $countTypes, ...$countParams);
    if (!mysqli_stmt_execute($stmtCnt)) { mysqli_stmt_close($stmtCnt); (new ErrorResult($clientMessages[$lang]['database_error']))->send(500); return; }
    $resCnt = mysqli_stmt_get_result($stmtCnt);
    $cntRow = $resCnt ? mysqli_fetch_assoc($resCnt) : ['cnt' => 0];
    $total = (int)($cntRow['cnt'] ?? 0);
    mysqli_stmt_close($stmtCnt);

    // Data query with filters, sorting, pagination
    $sql = "SELECT id, asset_id, type, amount, price, fees, notes, executed_at, created_at FROM portfolio_transactions WHERE asset_id = ?";
    $typesStr = 'i';
    $params = [$assetId];
    if ($type !== null && $type !== '' && $type !== 'all') { $sql .= " AND type = ?"; $typesStr .= 's'; $params[] = $type; }
    if ($dateFrom !== null) { $sql .= " AND executed_at >= ?"; $typesStr .= 's'; $params[] = $dateFrom; }
    if ($dateTo !== null) { $sql .= " AND executed_at <= ?"; $typesStr .= 's'; $params[] = $dateTo; }
    $sql .= " ORDER BY $orderCol $orderDir, id DESC LIMIT ? OFFSET ?";
    $typesStr .= 'ii';
    $params[] = (int)$limit;
    $params[] = (int)$offset;

    $stmt = mysqli_prepare($link, $sql);
    if (!$stmt) { (new ErrorResult($clientMessages[$lang]['database_error']))->send(500); return; }
    mysqli_stmt_bind_param($stmt, $typesStr, ...$params);
    if (!mysqli_stmt_execute($stmt)) { mysqli_stmt_close($stmt); (new ErrorResult($clientMessages[$lang]['database_error']))->send(500); return; }
    $res = mysqli_stmt_get_result($stmt);

    $items = [];
    if ($res) {
        while ($row = mysqli_fetch_assoc($res)) {
            $items[] = [
                'id' => (string)$row['id'],
                'assetId' => (string)$row['asset_id'],
                'type' => $row['type'],
                'quantity' => (float)$row['amount'],
                'amount' => (float)$row['amount'],
                'price' => (float)$row['price'],
                'totalValue' => (float)$row['amount'] * (float)$row['price'],
                'fees' => isset($row['fees']) ? (float)$row['fees'] : 0.0,
                'notes' => $row['notes'] ?? null,
                'executedAt' => date(DATE_ATOM, strtotime($row['executed_at'])),
                'createdAt' => date(DATE_ATOM, strtotime($row['created_at']))
            ];
        }
    }
    mysqli_stmt_close($stmt);

    $totalPages = max(1, (int)ceil($total / $limit));
    $meta = [
        'pagination' => [ 'page' => (int)$page, 'limit' => (int)$limit, 'total' => (int)$total, 'totalPages' => (int)$totalPages ],
        'generatedAt' => now_iso()
    ];
    (new SuccessResult($items, $meta))->send();
}

/* ============================
 * Analytics & Insights
 * ============================ */
function getPortfolioMetrics($userId, $id)
{
    $metrics = [
        'category_diversification' => ['score' => 72, 'contribution' => 18, 'maxScore' => 100, 'analysis' => 'Majors ağırlıklı'],
        'trend_category' => ['score' => 65, 'contribution' => 15, 'maxScore' => 100, 'analysis' => 'L1 ağırlığı yüksek'],
        'marketCapDistribution' => ['score' => 70, 'contribution' => 15, 'maxScore' => 100, 'analysis' => 'Large-cap oranı yüksek'],
        'portfolio_weight_score' => ['score' => 68, 'contribution' => 14, 'maxScore' => 100, 'analysis' => 'Kâr/zarar dengesi olumlu'],
        'stablecoinRatio' => ['score' => 55, 'contribution' => 10, 'maxScore' => 100, 'analysis' => 'Likidite tamponu sınırlı'],
        'portfolio_quality' => ['score' => 80, 'contribution' => 28, 'maxScore' => 100, 'analysis' => 'Proje kalitesi yüksek'],
        'volatility' => ['score' => 58, 'contribution' => 12, 'maxScore' => 100, 'analysis' => 'Orta volatilite'],
        'liquidityRisk' => ['score' => 62, 'contribution' => 12, 'maxScore' => 100, 'analysis' => 'Makûl likidite'],
        'totalScore' => 72
    ];
    (new SuccessResult($metrics))->send();
}
function getPortfolioSuggestions($userId, $id)
{
    $suggestions = [
        'suggestedChanges' => [
            [
                'assetId' => 'asset-usdt',
                'symbol' => 'USDT',
                'currentAllocation' => 4.2,
                'suggestedAllocation' => 10,
                'change' => 5.8,
                'reason' => 'Likidite tamponunu artırın',
                'impact' => 'medium',
                'priority' => 2
            ],
            [
                'assetId' => 'asset-sol',
                'symbol' => 'SOL',
                'currentAllocation' => 10.5,
                'suggestedAllocation' => 8,
                'change' => -2.5,
                'reason' => 'L1 ağırlığını azaltın',
                'impact' => 'low',
                'priority' => 3
            ]
        ],
        'potentialScoreImprovement' => 4.5,
        'improvementAreas' => ['Diversification', 'Stablecoin ratio'],
        'riskAssessment' => [
            'currentRisk' => 'medium',
            'projectedRisk' => 'low',
            'riskFactors' => ['High L1 exposure', 'Low cash buffer']
        ],
        'generatedAt' => now_iso()
    ];
    (new SuccessResult($suggestions))->send();
}
function refreshPortfolioPrices($userId, $id)
{
    // For now, simply return getPortfolio() data to simulate a refresh
    getPortfolio($userId, $id);
}

function createPortfolioFromTemplate($userId, $payload)
{
    global $link, $selectedLanguage, $clientMessages;
    $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';
    
    // Extract and validate input
    $name = isset($payload['name']) ? trim((string)$payload['name']) : '';
    $budget = isset($payload['budget']) ? (float)$payload['budget'] : null;
    $totalValue = isset($payload['totalValue']) ? (float)$payload['totalValue'] : null;
    $assets = isset($payload['assets']) ? $payload['assets'] : [];
    
    if ($name === '' || $budget === null || empty($assets)) {
        (new ErrorResult($clientMessages[$lang]['invalid_request']))->send(422);
        return;
    }
    
    // Validate total allocation adds up to 100%
    $totalAllocation = 0;
    foreach ($assets as $asset) {
        $totalAllocation += (float)($asset['allocation'] ?? 0);
    }
    
    if (abs($totalAllocation - 100) > 0.01) {
        (new ErrorResult('Total allocation must equal 100%'))->send(422);
        return;
    }
    
    // Start transaction
    mysqli_begin_transaction($link);
    
    try {
        // Create portfolio
        $stmt = mysqli_prepare($link, "INSERT INTO portfolio (user_id, name, description) VALUES (?, ?, ?)");
        if (!$stmt) {
            throw new Exception('Failed to prepare portfolio insert');
        }
        
        $description = 'AI generated portfolio with ' . count($assets) . ' assets';
        mysqli_stmt_bind_param($stmt, 'iss', $userId, $name, $description);
        
        if (!mysqli_stmt_execute($stmt)) {
            throw new Exception('Failed to create portfolio');
        }
        
        $portfolioId = mysqli_insert_id($link);
        mysqli_stmt_close($stmt);
        
        // Add each asset to the portfolio
        foreach ($assets as $asset) {
            $coinId = (int)($asset['coinId'] ?? 0);
            $allocation = (float)($asset['allocation'] ?? 0);
            
            if ($coinId <= 0 || $allocation <= 0) {
                continue;
            }
            
            // Get current price for the coin
            $currentPrice = null;
            $stmt = mysqli_prepare($link, "SELECT geckoslug FROM coindata WHERE id = ? LIMIT 1");
            if ($stmt) {
                mysqli_stmt_bind_param($stmt, 'i', $coinId);
                if (mysqli_stmt_execute($stmt)) {
                    $result = mysqli_stmt_get_result($stmt);
                    $row = mysqli_fetch_assoc($result);
                    if ($row && !empty($row['geckoslug'])) {
                        require_once __DIR__ . '/PriceService.php';
                        $currentPrice = get_current_price_by_coin_id($row['geckoslug'], ['isGeckoSlug' => true]);
                    }
                }
                mysqli_stmt_close($stmt);
            }
            
            if ($currentPrice === null || $currentPrice <= 0) {
                // Skip assets without valid price
                continue;
            }
            
            // Calculate investment amount based on allocation percentage
            $investmentAmount = ($allocation / 100) * $budget;
            
            // Calculate quantity based on current price
            $amount = $investmentAmount / $currentPrice;
            $averageCost = $currentPrice;
            
            // Check if coin is vetted
            $isVetted = 0;
            $stmt = mysqli_prepare($link, "SELECT 1 FROM client_coin_list WHERE id = ? LIMIT 1");
            if ($stmt) {
                mysqli_stmt_bind_param($stmt, 'i', $coinId);
                if (mysqli_stmt_execute($stmt)) {
                    $result = mysqli_stmt_get_result($stmt);
                    if (mysqli_fetch_assoc($result)) {
                        $isVetted = 1;
                    }
                }
                mysqli_stmt_close($stmt);
            }
            
            // Insert asset into portfolio
            $stmt = mysqli_prepare($link, "INSERT INTO portfolio_coins (portfolio_id, coin_id, average_cost, amount, is_vetted, added_at) VALUES (?, ?, ?, ?, ?, NOW())");
            if (!$stmt) {
                throw new Exception('Failed to prepare asset insert');
            }
            
            mysqli_stmt_bind_param($stmt, 'iiddi', $portfolioId, $coinId, $averageCost, $amount, $isVetted);
            
            if (!mysqli_stmt_execute($stmt)) {
                throw new Exception('Failed to add asset to portfolio');
            }
            
            mysqli_stmt_close($stmt);
        }
        
        // Commit transaction
        mysqli_commit($link);
        
        // Return the created portfolio
        $portfolio = [
            'id' => (string)$portfolioId,
            'name' => $name,
            'description' => $description,
            'totalValue' => $totalValue,
            'budget' => $budget,
            'assetCount' => count($assets),
            'createdAt' => now_iso()
        ];
        
        (new SuccessResult($portfolio))->send(201);
        
    } catch (Exception $e) {
        // Rollback transaction on error
        mysqli_rollback($link);
        (new ErrorResult($clientMessages[$lang]['database_error']))->send(500);
    }
}

function createPortfolioFromPredefinedTemplate($userId, $payload)
{
    global $link, $selectedLanguage, $clientMessages;
    $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';
    
    // Extract and validate input
    $templateId = isset($payload['templateId']) ? (int)$payload['templateId'] : null;
    $name = isset($payload['name']) ? trim((string)$payload['name']) : '';
    $budget = isset($payload['budget']) ? (float)$payload['budget'] : null;
    
    if (!$templateId || $name === '' || $budget === null || $budget <= 0) {
        (new ErrorResult($clientMessages[$lang]['invalid_request']))->send(422);
        return;
    }
    
    // Start transaction
    mysqli_begin_transaction($link);
    
    try {
        // Get predefined template
        $stmt = mysqli_prepare($link, "SELECT name, description FROM predefined_portfolio_templates WHERE id = ? AND is_active = 1 LIMIT 1");
        if (!$stmt) {
            throw new Exception('Failed to prepare template query');
        }
        
        mysqli_stmt_bind_param($stmt, 'i', $templateId);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $template = mysqli_fetch_assoc($result);
        mysqli_stmt_close($stmt);
        
        if (!$template) {
            throw new Exception('Template not found');
        }
        
        // Get template assets
        $stmt = mysqli_prepare($link, "SELECT coin_id, percentage FROM predefined_template_assets WHERE template_id = ?");
        if (!$stmt) {
            throw new Exception('Failed to prepare assets query');
        }
        
        mysqli_stmt_bind_param($stmt, 'i', $templateId);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $templateAssets = [];
        
        while ($row = mysqli_fetch_assoc($result)) {
            $templateAssets[] = [
                'coinId' => (int)$row['coin_id'],
                'percentage' => (float)$row['percentage']
            ];
        }
        mysqli_stmt_close($stmt);
        
        if (empty($templateAssets)) {
            throw new Exception('Template has no assets');
        }
        
        // Create portfolio
        $description = $template['description'] . ' (Template: ' . $template['name'] . ')';
        $stmt = mysqli_prepare($link, "INSERT INTO portfolio (user_id, name, description) VALUES (?, ?, ?)");
        if (!$stmt) {
            throw new Exception('Failed to prepare portfolio insert');
        }
        
        mysqli_stmt_bind_param($stmt, 'iss', $userId, $name, $description);
        if (!mysqli_stmt_execute($stmt)) {
            throw new Exception('Failed to create portfolio');
        }
        
        $portfolioId = mysqli_insert_id($link);
        mysqli_stmt_close($stmt);
        
        // Add each asset to the portfolio
        foreach ($templateAssets as $asset) {
            $coinId = $asset['coinId'];
            $percentage = $asset['percentage'];
            
            // Get current price for the coin
            $currentPrice = null;
            $stmt = mysqli_prepare($link, "SELECT geckoslug FROM coindata WHERE id = ? LIMIT 1");
            if ($stmt) {
                mysqli_stmt_bind_param($stmt, 'i', $coinId);
                if (mysqli_stmt_execute($stmt)) {
                    $result = mysqli_stmt_get_result($stmt);
                    $row = mysqli_fetch_assoc($result);
                    if ($row && !empty($row['geckoslug'])) {
                        require_once __DIR__ . '/PriceService.php';
                        $currentPrice = get_current_price_by_coin_id($row['geckoslug'], ['isGeckoSlug' => true]);
                    }
                }
                mysqli_stmt_close($stmt);
            }
            
            if ($currentPrice === null || $currentPrice <= 0) {
                // Skip assets without valid price
                continue;
            }
            
            // Calculate investment amount based on allocation percentage
            $investmentAmount = ($percentage / 100) * $budget;
            
            // Calculate quantity based on current price
            $amount = $investmentAmount / $currentPrice;
            $averageCost = $currentPrice;
            
            // Check if coin is vetted
            $isVetted = 0;
            $stmt = mysqli_prepare($link, "SELECT 1 FROM client_coin_list WHERE id = ? LIMIT 1");
            if ($stmt) {
                mysqli_stmt_bind_param($stmt, 'i', $coinId);
                if (mysqli_stmt_execute($stmt)) {
                    $result = mysqli_stmt_get_result($stmt);
                    if (mysqli_fetch_assoc($result)) {
                        $isVetted = 1;
                    }
                }
                mysqli_stmt_close($stmt);
            }
            
            // Insert asset into portfolio
            $stmt = mysqli_prepare($link, "INSERT INTO portfolio_coins (portfolio_id, coin_id, average_cost, amount, is_vetted, added_at) VALUES (?, ?, ?, ?, ?, NOW())");
            if (!$stmt) {
                throw new Exception('Failed to prepare asset insert');
            }
            
            mysqli_stmt_bind_param($stmt, 'iiddi', $portfolioId, $coinId, $averageCost, $amount, $isVetted);
            
            if (!mysqli_stmt_execute($stmt)) {
                throw new Exception('Failed to add asset to portfolio');
            }
            
            mysqli_stmt_close($stmt);
        }
        
        // Commit transaction
        mysqli_commit($link);
        
        // Return the created portfolio
        $portfolio = [
            'id' => (string)$portfolioId,
            'name' => $name,
            'description' => $description,
            'budget' => $budget,
            'templateId' => (string)$templateId,
            'assetCount' => count($templateAssets),
            'createdAt' => now_iso()
        ];
        
        (new SuccessResult($portfolio))->send(201);
        
    } catch (Exception $e) {
        // Rollback transaction on error
        mysqli_rollback($link);
        error_log('Create portfolio from template error: ' . $e->getMessage());
        (new ErrorResult($clientMessages[$lang]['database_error']))->send(500);
    }
}
