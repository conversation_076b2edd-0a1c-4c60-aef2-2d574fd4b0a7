<?php
/* ============================================================================================== */
/*                                        language options                                        */
/* ============================================================================================== */

/**
 * List of supported languages in the application
 * Used by multiple files to determine valid language selections
 */
$supportedLanguages = [
    "ar", // Arabic
    "de", // German
    "en", // English
    "es", // Spanish
    "fr", // French
    "hi", // Hindi
    "id", // Indonesian
    "it", // Italian
    "ja", // Japanese
    "ko", // Korean
    "pt", // Portuguese
    "ru", // Russian
    "tr", // Turkish
    "vi", // Vietnamese
    "zh"  // Chinese
];
/**
 * Helper function to determine the selected language based on headers
 *
 * @return string The selected language code
 */
function getSelectedLanguage()
{
    global $supportedLanguages;
    $headers = getallheaders();
    if (isset($headers['Content-Language']) && in_array(strtolower($headers['Content-Language']), $supportedLanguages)) {
        return strtolower($headers['Content-Language']);
    } else {
        return 'en'; // Default to English
    }
}
