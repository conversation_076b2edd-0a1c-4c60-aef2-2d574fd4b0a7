

<html>
    <head>
    </head>
    <body>
        <script type="text/javascript" src="https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js"></script>
        <div id="appleid-signin" data-color="black" data-border="true" data-type="sign in"></div>
        <script type="text/javascript">
            AppleID.auth.init({
                clientId : 'com.zdc.eviotlogin',
                scope : 'name email',
                redirectURI : 'https://api.coinscout.app/apple/callback.php',
                state : 'random_state_string',
                nonce : 'random_nonce_string',
                usePopup : true
            });


    // Başarıyla giriş yapılınca çalışacak event listener
    document.addEventListener('AppleIDSignInOnSuccess', (event) => {
                console.log("Apple Sign-In Successful:", event);
                
                // Apple Sign-In butonunu gizle
                document.getElementById("appleid-signin").style.display = "none";

                // Kullanıcı giriş yaptıysa bir mesaj gösterebiliriz
                document.body.innerHTML += "<p><PERSON><PERSON><PERSON> başarılı! Hoş geldiniz.</p>";
            });


// Listen for authorization failures.
document.addEventListener('AppleIDSignInOnFailure', (event) => {
     // Handle error.
     console.log(event.detail.error);
});




        </script>
    </body>
</html>

