<?php

ini_set('memory_limit', '-1');
set_time_limit(0);

include_once('config.php');

$webhookUrl = "https://dominexsus.app.n8n.cloud/webhook-test/5c9a3608-0673-4887-99fa-12777852e647";


global $link;




$our_data = array();
$obj2 = array();

$rs = mysqli_query($link, "SELECT coindata.*, coindata2.links from coindata
left join coindata2 on coindata.cr_id = coindata2.cr_id
order by id desc limit 100;");
$all_data = [];
while ($obj2 = mysqli_fetch_assoc($rs)) {




    $our_data[] = $obj2;

    $data['coin_name'] = $obj2['name'];
    $data['coin_id'] = $obj2['cr_id'];
    $data['coin_symbol'] = $obj2['symbol'];
    $data['links'] = $obj2['links'];


   array_push($all_data,$data);





}

    // Initialize cURL
    $ch = curl_init($webhookUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($all_data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
    ]);
    
    // Execute cURL and close
    $response = curl_exec($ch);
    
    //echo $response;
    
    curl_close($ch);






/*
// Sample data payload
$data = [
    'coin_name' => 'SOLANA',
    'links' => '[{"type":"web","value":"https:\/\/injective.com\/"},{"type":"medium","value":"https:\/\/blog.injective.com\/"},{"type":"reddit","value":"https:\/\/www.reddit.com\/r\/injective\/"},{"type":"telegram","value":"http:\/\/t.me\/joininjective"},{"type":"explorer","value":"https:\/\/explorer.injective.network\/"},{"type":"facebook","value":"https:\/\/www.facebook.com\/injectiveprotocol"},{"type":"whitepaper","value":"https:\/\/docs.injective.network\/"},{"type":"twitter","value":"https:\/\/twitter.com\/injective"},{"type":"github","value":"https:\/\/github.com\/InjectiveLabs\/"},{"type":"discord","value":"https:\/\/discord.gg\/injective"}] '

];


$ch = curl_init($webhookUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
]);

// Execute cURL and close
$response = curl_exec($ch);

//echo $response;

curl_close($ch);

*/



?>