<?php
/**
 * Stripe Email Notifications
 *
 * This file contains functions for sending email notifications related to Stripe subscriptions.
 */

require_once __DIR__ . '/../mailsender.php';
require_once __DIR__ . '/StripeLogger.php';

/**
 * Send subscription purchase confirmation email
 *
 * @param int $userId User ID
 * @param string $userEmail User email address
 * @param array $subscription Subscription details
 * @param string $planName Plan name
 * @param float $planAmount Plan amount
 * @param string $currency Currency code
 * @param string $interval Billing interval (month, year)
 * @param string $nextBillingDate Next billing date
 * @return bool Success status
 */
function sendSubscriptionPurchaseEmail($userId, $userEmail, $subscription, $planName, $planAmount, $currency, $interval, $nextBillingDate) {
    try {
        // E-posta adresi kontrolü
        if (empty($userEmail) || !filter_var($userEmail, FILTER_VALIDATE_EMAIL) || strpos($userEmail, '@placeholder.coinscout.app') !== false) {
            StripeLogger::log(StripeLogLevel::WARNING, "SKIPPING SUBSCRIPTION PURCHASE EMAIL - Invalid or placeholder email address - User ID: $userId, Email: $userEmail");
            return false;
        }

        // Format currency symbol
        $currencySymbol = getCurrencySymbol($currency);

        // Format plan interval
        $intervalText = ($interval == 'month') ? 'monthly' : 'yearly';

        // Create email subject
        $subject = "Your Coinscout Subscription Confirmation";

        // Create email body
        $messageBody = "
        <div style='display: flex; flex-direction: column; width: auto; border-width: 1px; border-style: solid; border-color: #334155; border-radius: 0.5rem; padding: 16px; background-color: #0f172a; color: #f8fafc; font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Oxygen, Ubuntu, Cantarell, \"Open Sans\", \"Helvetica Neue\", sans-serif;'>
            <div style='padding-bottom: 12px; border-bottom: 1px solid #1e293b; margin-bottom: 12px;'>
                <p style='font-size: 20px; font-weight: 600; color: #f8fafc; margin: 0;'>Subscription Confirmation</p>
                <p style='font-size: 14px; font-weight: 500; color: #94a3b8; margin: 4px 0 0 0;'>Thank you for subscribing to Coinscout!</p>
            </div>
            <div style='margin-top: 16px; padding: 12px; background-color: #1e293b; border-radius: 0.375rem;'>
                <p style='font-size: 14px; font-weight: 400; color: #f8fafc; margin: 0; line-height: 1.5;'>
                    Your subscription to <span style='font-weight: 600; color: #0ea5e9;'>{$planName}</span> has been successfully activated.
                </p>
                <div style='margin-top: 16px;'>
                    <p style='font-size: 14px; margin: 4px 0;'><strong>Subscription Details:</strong></p>
                    <ul style='list-style-type: none; padding-left: 8px; margin: 8px 0;'>
                        <li style='margin: 4px 0;'>Plan: {$planName}</li>
                        <li style='margin: 4px 0;'>Price: {$currencySymbol}{$planAmount} {$intervalText}</li>
                        <li style='margin: 4px 0;'>Next billing date: {$nextBillingDate}</li>
                    </ul>
                </div>
                <p style='font-size: 14px; margin-top: 16px;'>
                    You can manage your subscription from your account settings at any time.
                </p>
            </div>
            <div style='margin-top: 20px; padding-top: 16px; border-top: 1px solid #1e293b;'>
                <p style='font-size: 12px; color: #94a3b8; margin: 0;'>If you have any questions, please contact our support team at <a href='mailto:<EMAIL>' style='color: #0ea5e9; text-decoration: none;'><EMAIL></a></p>
            </div>
        </div>";

        // Send email
        send_mail($messageBody, [$userEmail], $subject);

        // Log success
        StripeLogger::log(StripeLogLevel::INFO, "SUBSCRIPTION PURCHASE EMAIL SENT - User ID: $userId, Email: $userEmail, Plan: $planName");

        return true;
    } catch (Exception $e) {
        // Log error
        StripeLogger::log(StripeLogLevel::ERROR, "FAILED TO SEND SUBSCRIPTION PURCHASE EMAIL: " . $e->getMessage(), [
            'exception' => get_class($e),
            'userId' => $userId,
            'email' => $userEmail
        ]);

        return false;
    }
}

/**
 * Send subscription upgrade confirmation email
 *
 * @param int $userId User ID
 * @param string $userEmail User email address
 * @param string $oldPlanName Old plan name
 * @param string $newPlanName New plan name
 * @param float $oldAmount Old plan amount
 * @param float $newAmount New plan amount
 * @param string $currency Currency code
 * @param string $nextBillingDate Next billing date
 * @return bool Success status
 */
function sendSubscriptionUpgradeEmail($userId, $userEmail, $oldPlanName, $newPlanName, $oldAmount, $newAmount, $currency, $nextBillingDate) {
    try {
        // E-posta adresi kontrolü
        if (empty($userEmail) || !filter_var($userEmail, FILTER_VALIDATE_EMAIL) || strpos($userEmail, '@placeholder.coinscout.app') !== false) {
            StripeLogger::log(StripeLogLevel::WARNING, "SKIPPING SUBSCRIPTION UPGRADE EMAIL - Invalid or placeholder email address - User ID: $userId, Email: $userEmail");
            return false;
        }

        // Format currency symbol
        $currencySymbol = getCurrencySymbol($currency);

        // Calculate price difference
        $priceDifference = $newAmount - $oldAmount;

        // Create email subject
        $subject = "Your Coinscout Subscription Has Been Upgraded";

        // Create email body
        $messageBody = "
        <div style='display: flex; flex-direction: column; width: auto; border-width: 1px; border-style: solid; border-color: #334155; border-radius: 0.5rem; padding: 16px; background-color: #0f172a; color: #f8fafc; font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Oxygen, Ubuntu, Cantarell, \"Open Sans\", \"Helvetica Neue\", sans-serif;'>
            <div style='padding-bottom: 12px; border-bottom: 1px solid #1e293b; margin-bottom: 12px;'>
                <p style='font-size: 20px; font-weight: 600; color: #f8fafc; margin: 0;'>Subscription Upgraded</p>
                <p style='font-size: 14px; font-weight: 500; color: #94a3b8; margin: 4px 0 0 0;'>Your Coinscout subscription has been upgraded!</p>
            </div>
            <div style='margin-top: 16px; padding: 12px; background-color: #1e293b; border-radius: 0.375rem;'>
                <p style='font-size: 14px; font-weight: 400; color: #f8fafc; margin: 0; line-height: 1.5;'>
                    Your subscription has been upgraded from <span style='font-weight: 600;'>{$oldPlanName}</span> to <span style='font-weight: 600; color: #0ea5e9;'>{$newPlanName}</span>.
                </p>
                <div style='margin-top: 16px;'>
                    <p style='font-size: 14px; margin: 4px 0;'><strong>Upgrade Details:</strong></p>
                    <ul style='list-style-type: none; padding-left: 8px; margin: 8px 0;'>
                        <li style='margin: 4px 0;'>Previous Plan: {$oldPlanName} ({$currencySymbol}{$oldAmount})</li>
                        <li style='margin: 4px 0;'>New Plan: {$newPlanName} ({$currencySymbol}{$newAmount})</li>
                        <li style='margin: 4px 0;'>Price Difference: {$currencySymbol}{$priceDifference}</li>
                        <li style='margin: 4px 0;'>Next billing date: {$nextBillingDate}</li>
                    </ul>
                </div>
                <p style='font-size: 14px; margin-top: 16px;'>
                    You now have access to all the features included in the {$newPlanName} plan. You can manage your subscription from your account settings at any time.
                </p>
            </div>
            <div style='margin-top: 20px; padding-top: 16px; border-top: 1px solid #1e293b;'>
                <p style='font-size: 12px; color: #94a3b8; margin: 0;'>If you have any questions, please contact our support team at <a href='mailto:<EMAIL>' style='color: #0ea5e9; text-decoration: none;'><EMAIL></a></p>
            </div>
        </div>";

        // Send email
        send_mail($messageBody, [$userEmail], $subject);

        // Log success
        StripeLogger::log(StripeLogLevel::INFO, "SUBSCRIPTION UPGRADE EMAIL SENT - User ID: $userId, Email: $userEmail, From: $oldPlanName, To: $newPlanName");

        return true;
    } catch (Exception $e) {
        // Log error
        StripeLogger::log(StripeLogLevel::ERROR, "FAILED TO SEND SUBSCRIPTION UPGRADE EMAIL: " . $e->getMessage(), [
            'exception' => get_class($e),
            'userId' => $userId,
            'email' => $userEmail
        ]);

        return false;
    }
}

/**
 * Send subscription downgrade confirmation email
 *
 * @param int $userId User ID
 * @param string $userEmail User email address
 * @param string $oldPlanName Old plan name
 * @param string $newPlanName New plan name
 * @param string $effectiveDate Date when downgrade takes effect
 * @return bool Success status
 */
function sendSubscriptionDowngradeEmail($userId, $userEmail, $oldPlanName, $newPlanName, $effectiveDate) {
    try {
        // E-posta adresi kontrolü
        if (empty($userEmail) || !filter_var($userEmail, FILTER_VALIDATE_EMAIL) || strpos($userEmail, '@placeholder.coinscout.app') !== false) {
            StripeLogger::log(StripeLogLevel::WARNING, "SKIPPING SUBSCRIPTION DOWNGRADE EMAIL - Invalid or placeholder email address - User ID: $userId, Email: $userEmail");
            return false;
        }

        // Create email subject
        $subject = "Your Coinscout Subscription Change Confirmation";

        // Create email body
        $messageBody = "
        <div style='display: flex; flex-direction: column; width: auto; border-width: 1px; border-style: solid; border-color: #334155; border-radius: 0.5rem; padding: 16px; background-color: #0f172a; color: #f8fafc; font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Oxygen, Ubuntu, Cantarell, \"Open Sans\", \"Helvetica Neue\", sans-serif;'>
            <div style='padding-bottom: 12px; border-bottom: 1px solid #1e293b; margin-bottom: 12px;'>
                <p style='font-size: 20px; font-weight: 600; color: #f8fafc; margin: 0;'>Subscription Change Confirmation</p>
                <p style='font-size: 14px; font-weight: 500; color: #94a3b8; margin: 4px 0 0 0;'>Your Coinscout subscription has been updated</p>
            </div>
            <div style='margin-top: 16px; padding: 12px; background-color: #1e293b; border-radius: 0.375rem;'>
                <p style='font-size: 14px; font-weight: 400; color: #f8fafc; margin: 0; line-height: 1.5;'>
                    We've processed your request to change your subscription from <span style='font-weight: 600;'>{$oldPlanName}</span> to <span style='font-weight: 600;'>{$newPlanName}</span>.
                </p>
                <div style='margin-top: 16px;'>
                    <p style='font-size: 14px; margin: 4px 0;'><strong>Change Details:</strong></p>
                    <ul style='list-style-type: none; padding-left: 8px; margin: 8px 0;'>
                        <li style='margin: 4px 0;'>Current Plan: {$oldPlanName}</li>
                        <li style='margin: 4px 0;'>New Plan: {$newPlanName}</li>
                        <li style='margin: 4px 0;'>Effective Date: {$effectiveDate}</li>
                    </ul>
                </div>
                <p style='font-size: 14px; margin-top: 16px;'>
                    Your subscription will change to {$newPlanName} on {$effectiveDate}. Until then, you'll continue to have access to all features in your current plan.
                </p>
            </div>
            <div style='margin-top: 20px; padding-top: 16px; border-top: 1px solid #1e293b;'>
                <p style='font-size: 12px; color: #94a3b8; margin: 0;'>If you have any questions, please contact our support team at <a href='mailto:<EMAIL>' style='color: #0ea5e9; text-decoration: none;'><EMAIL></a></p>
            </div>
        </div>";

        // Send email
        send_mail($messageBody, [$userEmail], $subject);

        // Log success
        StripeLogger::log(StripeLogLevel::INFO, "SUBSCRIPTION DOWNGRADE EMAIL SENT - User ID: $userId, Email: $userEmail, From: $oldPlanName, To: $newPlanName, Effective: $effectiveDate");

        return true;
    } catch (Exception $e) {
        // Log error
        StripeLogger::log(StripeLogLevel::ERROR, "FAILED TO SEND SUBSCRIPTION DOWNGRADE EMAIL: " . $e->getMessage(), [
            'exception' => get_class($e),
            'userId' => $userId,
            'email' => $userEmail
        ]);

        return false;
    }
}

/**
 * Send subscription cancellation confirmation email
 *
 * @param int $userId User ID
 * @param string $userEmail User email address
 * @param string $planName Plan name
 * @param string $endDate End date of subscription
 * @param bool $immediateCancel Whether cancellation is immediate
 * @return bool Success status
 */
function sendSubscriptionCancellationEmail($userId, $userEmail, $planName, $endDate, $immediateCancel = false) {
    try {
        // E-posta adresi kontrolü
        if (empty($userEmail) || !filter_var($userEmail, FILTER_VALIDATE_EMAIL) || strpos($userEmail, '@placeholder.coinscout.app') !== false) {
            StripeLogger::log(StripeLogLevel::WARNING, "SKIPPING SUBSCRIPTION CANCELLATION EMAIL - Invalid or placeholder email address - User ID: $userId, Email: $userEmail");
            return false;
        }

        // Create email subject
        $subject = "Your Coinscout Subscription Cancellation";

        // Create message based on immediate cancellation or end of period
        $cancellationMessage = $immediateCancel
            ? "Your subscription has been cancelled immediately. You no longer have access to premium features."
            : "Your subscription will remain active until the end of your current billing period ({$endDate}). After that date, you will no longer be charged, and your account will revert to the free plan.";

        // Create email body
        $messageBody = "
        <div style='display: flex; flex-direction: column; width: auto; border-width: 1px; border-style: solid; border-color: #334155; border-radius: 0.5rem; padding: 16px; background-color: #0f172a; color: #f8fafc; font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Oxygen, Ubuntu, Cantarell, \"Open Sans\", \"Helvetica Neue\", sans-serif;'>
            <div style='padding-bottom: 12px; border-bottom: 1px solid #1e293b; margin-bottom: 12px;'>
                <p style='font-size: 20px; font-weight: 600; color: #f8fafc; margin: 0;'>Subscription Cancellation</p>
                <p style='font-size: 14px; font-weight: 500; color: #94a3b8; margin: 4px 0 0 0;'>Your Coinscout subscription has been cancelled</p>
            </div>
            <div style='margin-top: 16px; padding: 12px; background-color: #1e293b; border-radius: 0.375rem;'>
                <p style='font-size: 14px; font-weight: 400; color: #f8fafc; margin: 0; line-height: 1.5;'>
                    We've processed your request to cancel your <span style='font-weight: 600;'>{$planName}</span> subscription.
                </p>
                <div style='margin-top: 16px;'>
                    <p style='font-size: 14px; margin: 4px 0;'>{$cancellationMessage}</p>
                </div>
                <p style='font-size: 14px; margin-top: 16px;'>
                    We're sorry to see you go. If you change your mind, you can reactivate your subscription at any time from your account settings.
                </p>
            </div>
            <div style='margin-top: 20px; padding-top: 16px; border-top: 1px solid #1e293b;'>
                <p style='font-size: 12px; color: #94a3b8; margin: 0;'>If you have any questions, please contact our support team at <a href='mailto:<EMAIL>' style='color: #0ea5e9; text-decoration: none;'><EMAIL></a></p>
            </div>
        </div>";

        // Send email
        send_mail($messageBody, [$userEmail], $subject);

        // Log success
        StripeLogger::log(StripeLogLevel::INFO, "SUBSCRIPTION CANCELLATION EMAIL SENT - User ID: $userId, Email: $userEmail, Plan: $planName, End Date: $endDate");

        return true;
    } catch (Exception $e) {
        // Log error
        StripeLogger::log(StripeLogLevel::ERROR, "FAILED TO SEND SUBSCRIPTION CANCELLATION EMAIL: " . $e->getMessage(), [
            'exception' => get_class($e),
            'userId' => $userId,
            'email' => $userEmail
        ]);

        return false;
    }
}

/**
 * Get currency symbol from currency code
 *
 * @param string $currency Currency code
 * @return string Currency symbol
 */
function getCurrencySymbol($currency) {
    $symbols = [
        'USD' => '$',
        'EUR' => '€',
        'GBP' => '£',
        'TRY' => '₺',
        'JPY' => '¥',
        'CNY' => '¥',
        'INR' => '₹'
    ];

    return isset($symbols[strtoupper($currency)]) ? $symbols[strtoupper($currency)] : $currency;
}
